using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace CalculateOffset
{
    /// <summary>
    /// 线程泄漏测试工具
    /// </summary>
    public static class ThreadLeakTest
    {
        /// <summary>
        /// 获取当前进程的线程数
        /// </summary>
        public static int GetCurrentThreadCount()
        {
            try
            {
                Process currentProcess = Process.GetCurrentProcess();
                return currentProcess.Threads.Count;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 获取线程数失败: {ex.Message}");
                return -1;
            }
        }

        /// <summary>
        /// 监控线程数变化
        /// </summary>
        public static void MonitorThreadCount(string operation, int initialThreadCount)
        {
            int currentThreadCount = GetCurrentThreadCount();
            int threadDifference = currentThreadCount - initialThreadCount;
            
            if (threadDifference > 0)
            {
                Console.WriteLine($"⚠️ [{operation}] 线程数增加: {initialThreadCount} -> {currentThreadCount} (+{threadDifference})");
            }
            else if (threadDifference < 0)
            {
                Console.WriteLine($"✅ [{operation}] 线程数减少: {initialThreadCount} -> {currentThreadCount} ({threadDifference})");
            }
            else
            {
                Console.WriteLine($"✅ [{operation}] 线程数无变化: {currentThreadCount}");
            }
        }

        /// <summary>
        /// 等待线程数稳定
        /// </summary>
        public static async Task<int> WaitForThreadCountStable(int maxWaitSeconds = 10)
        {
            int lastCount = GetCurrentThreadCount();
            int stableCount = 0;
            
            for (int i = 0; i < maxWaitSeconds * 2; i++) // 每500ms检查一次
            {
                await Task.Delay(500);
                int currentCount = GetCurrentThreadCount();
                
                if (currentCount == lastCount)
                {
                    stableCount++;
                    if (stableCount >= 4) // 连续2秒稳定
                    {
                        Console.WriteLine($"✅ 线程数已稳定在: {currentCount}");
                        return currentCount;
                    }
                }
                else
                {
                    stableCount = 0;
                    lastCount = currentCount;
                }
            }
            
            Console.WriteLine($"⚠️ 线程数在{maxWaitSeconds}秒内未稳定，当前: {lastCount}");
            return lastCount;
        }

        /// <summary>
        /// 检测是否存在线程泄漏
        /// </summary>
        public static async Task<bool> DetectThreadLeak(int beforeCount, int afterCount, int toleranceCount = 2)
        {
            // 等待线程数稳定
            int finalCount = await WaitForThreadCountStable();
            
            int leakedThreads = finalCount - beforeCount;
            
            if (leakedThreads > toleranceCount)
            {
                Console.WriteLine($"🚨 检测到线程泄漏! 泄漏线程数: {leakedThreads} (容忍度: {toleranceCount})");
                Console.WriteLine($"   操作前: {beforeCount}, 操作后: {afterCount}, 最终: {finalCount}");
                return true;
            }
            else
            {
                Console.WriteLine($"✅ 未检测到线程泄漏. 线程变化: {leakedThreads} (容忍度: {toleranceCount})");
                Console.WriteLine($"   操作前: {beforeCount}, 操作后: {afterCount}, 最终: {finalCount}");
                return false;
            }
        }

        /// <summary>
        /// 打印当前线程信息
        /// </summary>
        public static void PrintThreadInfo()
        {
            try
            {
                Process currentProcess = Process.GetCurrentProcess();
                Console.WriteLine($"📊 当前进程线程信息:");
                Console.WriteLine($"   进程ID: {currentProcess.Id}");
                Console.WriteLine($"   线程总数: {currentProcess.Threads.Count}");
                Console.WriteLine($"   工作集: {currentProcess.WorkingSet64 / 1024 / 1024} MB");
                Console.WriteLine($"   虚拟内存: {currentProcess.VirtualMemorySize64 / 1024 / 1024} MB");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 获取线程信息失败: {ex.Message}");
            }
        }
    }
}
