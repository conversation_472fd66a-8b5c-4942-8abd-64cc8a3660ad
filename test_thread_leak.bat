@echo off
echo 测试线程泄漏修复...

echo 启动应用程序前的线程数:
tasklist /fi "imagename eq CalculateOffset.exe" 2>nul | find /c "CalculateOffset.exe"

echo.
echo 启动应用程序...
start "" "bin\Debug\CalculateOffset.exe"

echo.
echo 等待5秒让应用程序完全启动...
timeout /t 5 /nobreak >nul

echo.
echo 应用程序启动后的线程数:
tasklist /fi "imagename eq CalculateOffset.exe" 2>nul | find /c "CalculateOffset.exe"

echo.
echo 请在应用程序中进行计算操作，然后点击取消按钮测试线程泄漏修复...
echo 测试完成后，请关闭应用程序。
echo.
pause

echo.
echo 应用程序关闭后的线程数:
tasklist /fi "imagename eq CalculateOffset.exe" 2>nul | find /c "CalculateOffset.exe"

echo.
echo 测试完成！
pause
