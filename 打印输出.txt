?? [T1] 开始执行: 总体计算时间
??? 会话LCM已清除
  ?? [T1] 开始执行: 会话LCM计算(一次性)
?? 会话LCM已设置: 100000us (100.0ms)
  ? [T1] 完成执行: 会话LCM计算(一次性) - 耗时: 0ms
  ?? [T1] 开始执行: 任务模式计算
  ? [T1] 完成执行: 任务模式计算 - 耗时: 10ms
? [T1] 完成执行: 总体计算时间 - 耗时: 14ms
?? ==================== 性能统计摘要 ====================
?? [T3] 开始执行: 智能调度引擎执行
?? 总体计算时间:
   平均耗时: 14ms
   调用次数: 1次
   总耗时: 14ms

?? 任务模式计算:
   平均耗时: 10ms
   调用次数: 1次
   总耗时: 10ms

?? 会话LCM计算(一次性):
   平均耗时: 0ms
   调用次数: 1次
   总耗时: 0ms

?? ====================================================

  ?? [T3] 开始执行: OffsetCalculator种子生成
    ?? [T3] 开始执行: OffsetCalculator内部计算
      ?? [T3] 开始执行: PerformOffsetCalculation
        ?? [T3] 开始执行: FindBestOffset-T2
        ? [T3] 完成执行: FindBestOffset-T2 - 耗时: 3ms
        ?? [T3] 开始执行: FindBestOffset-T3
        ? [T3] 完成执行: FindBestOffset-T3 - 耗时: 0ms
        ?? [T3] 开始执行: FindBestOffset-T4
        ? [T3] 完成执行: FindBestOffset-T4 - 耗时: 0ms
      ? [T3] 完成执行: PerformOffsetCalculation - 耗时: 4ms
      ?? [T3] 开始执行: EvaluateVariance
      ? [T3] 完成执行: EvaluateVariance - 耗时: 1ms
    ? [T3] 完成执行: OffsetCalculator内部计算 - 耗时: 10ms
  ? [T3] 完成执行: OffsetCalculator种子生成 - 耗时: 12ms
  ?? [T3] 开始执行: 双轨优化-MaxOffset10ms
    ?? [T3] 开始执行: OffsetCalculator种子生成
      ?? [T3] 开始执行: OffsetCalculator内部计算
        ?? [T3] 开始执行: PerformOffsetCalculation
          ?? [T3] 开始执行: FindBestOffset-T2
          ? [T3] 完成执行: FindBestOffset-T2 - 耗时: 0ms
          ?? [T3] 开始执行: FindBestOffset-T3
          ? [T3] 完成执行: FindBestOffset-T3 - 耗时: 0ms
          ?? [T3] 开始执行: FindBestOffset-T4
          ? [T3] 完成执行: FindBestOffset-T4 - 耗时: 0ms
        ? [T3] 完成执行: PerformOffsetCalculation - 耗时: 0ms
        ?? [T3] 开始执行: EvaluateVariance
        ? [T3] 完成执行: EvaluateVariance - 耗时: 0ms
      ? [T3] 完成执行: OffsetCalculator内部计算 - 耗时: 0ms
    ? [T3] 完成执行: OffsetCalculator种子生成 - 耗时: 0ms
    ?? [T3] 开始执行: 遗传算法优化
?? [T4] 开始执行: 模拟退火算法优化
  ?? [T4] 开始执行: SA重启循环
    ?? [T4] 开始执行: SA第1轮退火
      ?? [T4] 开始执行: SA初始解生成
      ? [T4] 完成执行: SA初始解生成 - 耗时: 0ms
      ?? [T4] 开始执行: SA初始解评估
        ?? [T4] 开始执行: SA任务克隆应用
        ? [T4] 完成执行: SA任务克隆应用 - 耗时: 0ms
      ? [T4] 完成执行: SA初始解评估 - 耗时: 0ms
      ?? [T4] 开始执行: SA温度计算
      ? [T4] 完成执行: SA温度计算 - 耗时: 6ms
      ?? [T4] 开始执行: SA控制器初始化
      ? [T4] 完成执行: SA控制器初始化 - 耗时: 0ms
      ?? [T4] 开始执行: SA主优化循环
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 224ms
    ?? [T3] 开始执行: 遗传算法优化
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 187ms
    ?? [T3] 开始执行: 遗传算法优化
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 178ms
    ?? [T3] 开始执行: 遗传算法优化
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 175ms
    ?? [T3] 开始执行: 遗传算法优化
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 178ms
    ?? [T3] 开始执行: OffsetCalculator种子生成
    ? [T3] 完成执行: OffsetCalculator种子生成 - 耗时: 0ms
    ?? [T3] 开始执行: 遗传算法优化
    ? [T3] 完成执行: 遗传算法优化 - 耗时: 234ms
      ? [T4] 完成执行: SA主优化循环 - 耗时: 2.83s
      ?? [T4] 开始执行: SA最优解选择
线程 12024 已退出，返回值为 0 (0x0)。
线程 12992 已退出，返回值为 0 (0x0)。
      ? [T4] 完成执行: SA最优解选择 - 耗时: 21.92s
    ? [T4] 完成执行: SA第1轮退火 - 耗时: 24.76s
    ?? [T4] 开始执行: SA第2轮退火
      ?? [T4] 开始执行: SA初始解生成
      ? [T4] 完成执行: SA初始解生成 - 耗时: 0ms
      ?? [T4] 开始执行: SA初始解评估
        ?? [T4] 开始执行: SA任务克隆应用
        ? [T4] 完成执行: SA任务克隆应用 - 耗时: 0ms
      ? [T4] 完成执行: SA初始解评估 - 耗时: 0ms
      ?? [T4] 开始执行: SA温度计算
      ? [T4] 完成执行: SA温度计算 - 耗时: 5ms
      ?? [T4] 开始执行: SA控制器初始化
      ? [T4] 完成执行: SA控制器初始化 - 耗时: 0ms
      ?? [T4] 开始执行: SA主优化循环
      ? [T4] 完成执行: SA主优化循环 - 耗时: 512ms
      ?? [T4] 开始执行: SA最优解选择
      ? [T4] 完成执行: SA最优解选择 - 耗时: 6.70s
    ? [T4] 完成执行: SA第2轮退火 - 耗时: 7.22s
    ?? [T4] 开始执行: SA第3轮退火
      ?? [T4] 开始执行: SA初始解生成
      ? [T4] 完成执行: SA初始解生成 - 耗时: 0ms
      ?? [T4] 开始执行: SA初始解评估
        ?? [T4] 开始执行: SA任务克隆应用
        ? [T4] 完成执行: SA任务克隆应用 - 耗时: 0ms
      ? [T4] 完成执行: SA初始解评估 - 耗时: 0ms
      ?? [T4] 开始执行: SA温度计算
      ? [T4] 完成执行: SA温度计算 - 耗时: 19ms
      ?? [T4] 开始执行: SA控制器初始化
      ? [T4] 完成执行: SA控制器初始化 - 耗时: 0ms
      ?? [T4] 开始执行: SA主优化循环
      ? [T4] 完成执行: SA主优化循环 - 耗时: 686ms
      ?? [T4] 开始执行: SA最优解选择
      ? [T4] 完成执行: SA最优解选择 - 耗时: 9.56s
    ? [T4] 完成执行: SA第3轮退火 - 耗时: 10.26s
  ? [T4] 完成执行: SA重启循环 - 耗时: 42.24s
? [T4] 完成执行: 模拟退火算法优化 - 耗时: 42.25s
?? [T4] 开始执行: 模拟退火算法优化
  ?? [T4] 开始执行: SA重启循环
    ?? [T4] 开始执行: SA第1轮退火
      ?? [T4] 开始执行: SA初始解生成
      ? [T4] 完成执行: SA初始解生成 - 耗时: 0ms
      ?? [T4] 开始执行: SA初始解评估
        ?? [T4] 开始执行: SA任务克隆应用
        ? [T4] 完成执行: SA任务克隆应用 - 耗时: 0ms
      ? [T4] 完成执行: SA初始解评估 - 耗时: 0ms
      ?? [T4] 开始执行: SA温度计算
      ? [T4] 完成执行: SA温度计算 - 耗时: 6ms
      ?? [T4] 开始执行: SA控制器初始化
      ? [T4] 完成执行: SA控制器初始化 - 耗时: 0ms
      ?? [T4] 开始执行: SA主优化循环
      ? [T4] 完成执行: SA主优化循环 - 耗时: 2.26s
      ?? [T4] 开始执行: SA最优解选择