using System;
using System.ComponentModel;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CalculateOffset
{
    /// <summary>
    /// 计算进度显示窗口
    /// </summary>
    public partial class ProgressForm : Form
    {
        private ProgressBar progressBar;
        private Label statusLabel;
        private Label detailLabel;
        private Button cancelButton;
        private bool _cancellationRequested = false;
        private CancellationTokenSource _cancellationTokenSource;
        
        // 自动进度相关
        private System.Windows.Forms.Timer _autoProgressTimer;
        private int _currentAutoProgress = 0;
        private int _autoProgressIncrement = 1;
        private int _autoProgressTargetMax = 85; // 目标最大进度
        private bool _autoProgressEnabled = false;
        
        // 主窗体引用，用于取消时立即恢复按钮状态
        private Form1 _parentForm = null;

        public bool CancellationRequested => _cancellationRequested;
        public CancellationToken CancellationToken => _cancellationTokenSource?.Token ?? CancellationToken.None;

        public ProgressForm(string title = "计算进度", Form1 parentForm = null)
        {
            InitializeComponent();
            this.Text = title;
            _parentForm = parentForm;
            _cancellationTokenSource = new CancellationTokenSource();
            InitializeAutoProgressTimer();
        }

        private void InitializeComponent()
        {
            this.progressBar = new ProgressBar();
            this.statusLabel = new Label();
            this.detailLabel = new Label();
            this.cancelButton = new Button();
            this.SuspendLayout();

            // ProgressBar
            this.progressBar.Location = new System.Drawing.Point(20, 40);
            this.progressBar.Size = new System.Drawing.Size(360, 23);
            this.progressBar.Style = ProgressBarStyle.Continuous;

            // StatusLabel
            this.statusLabel.Location = new System.Drawing.Point(20, 15);
            this.statusLabel.Size = new System.Drawing.Size(360, 20);
            this.statusLabel.Text = "正在初始化...";
            this.statusLabel.ForeColor = System.Drawing.Color.Blue;

            // DetailLabel
            this.detailLabel.Location = new System.Drawing.Point(20, 75);
            this.detailLabel.Size = new System.Drawing.Size(360, 40);
            this.detailLabel.Text = "";
            this.detailLabel.ForeColor = System.Drawing.Color.Gray;

            // CancelButton
            this.cancelButton.Location = new System.Drawing.Point(160, 125);
            this.cancelButton.Size = new System.Drawing.Size(80, 25);
            this.cancelButton.Text = "取消";
            this.cancelButton.UseVisualStyleBackColor = true;
            this.cancelButton.Click += new System.EventHandler(this.CancelButton_Click);

            // ProgressForm
            this.ClientSize = new System.Drawing.Size(400, 170);
            this.Controls.Add(this.progressBar);
            this.Controls.Add(this.statusLabel);
            this.Controls.Add(this.detailLabel);
            this.Controls.Add(this.cancelButton);
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.StartPosition = FormStartPosition.CenterParent;
            this.TopMost = true;
            this.ResumeLayout(false);
        }

        /// <summary>
        /// 更新进度（线程安全）
        /// </summary>
        /// <param name="percentage">进度百分比 (0-100)</param>
        /// <param name="status">状态信息</param>
        /// <param name="detail">详细信息</param>
        public void UpdateProgress(int percentage, string status = null, string detail = null)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int, string, string>(UpdateProgress), percentage, status, detail);
                return;
            }

            // 智能进度更新策略
            if (percentage >= 0 && percentage <= 100)
            {
                bool shouldUpdate = false;
                
                if (!_autoProgressEnabled)
                {
                    // 自动进度未启用，直接设置
                    shouldUpdate = true;
                }
                else if (percentage > _currentAutoProgress)
                {
                    // 真实进度超过自动进度，使用真实进度
                    shouldUpdate = true;
                    Console.WriteLine($"📈 真实进度超过自动进度：{_currentAutoProgress}% -> {percentage}%，使用真实进度");
                    
                    // 如果进度跳跃较大，可能是新轮次开始，更新自动进度基准
                    if (percentage - _currentAutoProgress > 10)
                    {
                        Console.WriteLine($"📈 检测到进度跳跃：{_currentAutoProgress}% -> {percentage}%，可能是新轮次开始");
                    }
                }
                else if (percentage >= 90)
                {
                    // 接近完成，停止自动进度并使用真实进度
                    if (_autoProgressEnabled)
                    {
                        StopAutoProgress();
                    }
                    shouldUpdate = true;
                }
                else
                {
                    // 自动进度高于或等于真实进度，继续使用自动进度
                    Console.WriteLine($"⏳ 自动进度({_currentAutoProgress}%)高于或等于真实进度({percentage}%)，继续自动增长");
                    // 注意：这里不设置shouldUpdate=true，让自动进度继续工作
                }
                // 否则保持自动进度继续运行
                
                if (shouldUpdate)
                {
                    progressBar.Value = percentage;
                    _currentAutoProgress = percentage; // 同步自动进度状态
                }
            }

            if (!string.IsNullOrEmpty(status))
            {
                statusLabel.Text = status;
            }

            if (!string.IsNullOrEmpty(detail))
            {
                detailLabel.Text = detail;
            }

            Application.DoEvents(); // 确保UI更新
        }

        /// <summary>
        /// 初始化自动进度定时器
        /// </summary>
        private void InitializeAutoProgressTimer()
        {
            _autoProgressTimer = new System.Windows.Forms.Timer();
            _autoProgressTimer.Interval = 1000; // 每1秒更新一次，更快响应
            _autoProgressTimer.Tick += AutoProgressTimer_Tick;
        }

        /// <summary>
        /// 启动自动进度增长
        /// </summary>
        /// <param name="startProgress">起始进度</param>
        /// <param name="endProgress">结束进度（可选，默认85%）</param>
        public void StartAutoProgress(int startProgress = 10, int endProgress = 85)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int, int>(StartAutoProgress), startProgress, endProgress);
                return;
            }

            if (_autoProgressEnabled) 
            {
                Console.WriteLine("⚠️ 自动进度已启用，先停止再重新启动");
                StopAutoProgress();
            }

            _currentAutoProgress = startProgress;
            _autoProgressTargetMax = endProgress;
            _autoProgressIncrement = 1;
            _autoProgressEnabled = true;
            
            // 设置初始进度
            UpdateProgress(_currentAutoProgress, null, "正在计算中，请稍候...");
            
            // 确保在UI线程中启动Timer
            _autoProgressTimer.Start();
            Console.WriteLine($"🔄 启动分段自动进度增长，范围: {startProgress}% -> {endProgress}%");
            Console.WriteLine($"🔄 Timer间隔: {_autoProgressTimer.Interval}ms, 增长幅度: {_autoProgressIncrement}%");
            Console.WriteLine($"🔄 Timer启用状态: {_autoProgressTimer.Enabled}, 自动进度启用: {_autoProgressEnabled}");
            
            // 立即触发一次测试，确保Timer正常工作
            Console.WriteLine($"🧪 基础自动进度测试触发...");
            Task.Run(async () =>
            {
                await Task.Delay(300); // 等待300ms
                this.Invoke(new Action(() =>
                {
                    Console.WriteLine($"🧪 手动触发基础Timer Tick进行测试");
                    AutoProgressTimer_Tick(_autoProgressTimer, EventArgs.Empty);
                }));
            });
        }

        /// <summary>
        /// 启动轮次内自动进度增长（为特定轮次分配进度范围）
        /// </summary>
        /// <param name="roundStartProgress">轮次起始进度</param>
        /// <param name="roundEndProgress">轮次结束进度</param>
        /// <param name="roundName">轮次名称（用于日志）</param>
        public void StartRoundAutoProgress(int roundStartProgress, int roundEndProgress, string roundName = "计算轮次")
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<int, int, string>(StartRoundAutoProgress), roundStartProgress, roundEndProgress, roundName);
                return;
            }

            Console.WriteLine($"🔄 尝试启动轮次自动进度：{roundName} [{roundStartProgress}% -> {roundEndProgress}%]");

            if (_autoProgressEnabled) 
            {
                Console.WriteLine("🔄 停止之前的自动进度");
                _autoProgressTimer.Stop();
                _autoProgressEnabled = false;
            }

            _currentAutoProgress = roundStartProgress;
            _autoProgressTargetMax = roundEndProgress - 2; // 留2%余量给轮次完成设置
            _autoProgressIncrement = 1;
            _autoProgressEnabled = true;
            
            // 根据轮次范围调整定时器间隔（更频繁的更新）
            int progressRange = roundEndProgress - roundStartProgress;
            if (progressRange <= 10)
            {
                _autoProgressTimer.Interval = 20000; // 20秒
                _autoProgressIncrement = 1;
            }
            else if (progressRange <= 20)
            {
                _autoProgressTimer.Interval = 15000; // 15秒
                _autoProgressIncrement = 1;
            }
            else
            {
                _autoProgressTimer.Interval = 10000; // 10秒
                _autoProgressIncrement = 2;
            }
            
            // 强制重新启动Timer
            _autoProgressTimer.Stop();
            _autoProgressTimer.Start();
            
            Console.WriteLine($"🔄 轮次自动进度已启动：{roundName} [{roundStartProgress}% -> {roundEndProgress}%]");
            Console.WriteLine($"🔄 轮次Timer设置: 间隔={_autoProgressTimer.Interval}ms, 增长幅度={_autoProgressIncrement}%, 目标={_autoProgressTargetMax}%");
            Console.WriteLine($"🔄 Timer启用状态: {_autoProgressTimer.Enabled}, 自动进度启用: {_autoProgressEnabled}");
            
            // 立即触发一次测试
            Console.WriteLine($"🧪 测试触发Timer事件...");
            var testTask = Task.Run(async () =>
            {
                try
                {
                    await Task.Delay(500, _cancellationTokenSource.Token); // 等待500ms，支持取消
                    if (!_cancellationRequested)
                    {
                        this.Invoke(new Action(() =>
                        {
                            if (!_cancellationRequested)
                            {
                                Console.WriteLine($"🧪 手动触发Timer Tick进行测试");
                                AutoProgressTimer_Tick(_autoProgressTimer, EventArgs.Empty);
                            }
                        }));
                    }
                }
                catch (OperationCanceledException)
                {
                    Console.WriteLine("🚫 测试Timer任务被取消");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ 测试Timer任务异常: {ex.Message}");
                }
            });
        }

        /// <summary>
        /// 停止自动进度增长
        /// </summary>
        public void StopAutoProgress()
        {
            if (!_autoProgressEnabled) return;

            _autoProgressEnabled = false;
            _autoProgressTimer.Stop();
            Console.WriteLine($"⏹️ 停止自动进度增长，当前进度: {_currentAutoProgress}%");
        }

        /// <summary>
        /// 自动进度定时器事件
        /// </summary>
        private void AutoProgressTimer_Tick(object sender, EventArgs e)
        {
            Console.WriteLine($"⏰ Timer Tick触发！时间:{DateTime.Now:HH:mm:ss.fff}");
            
            if (!_autoProgressEnabled || _cancellationRequested) 
            {
                Console.WriteLine($"⏸️ 自动进度暂停：enabled={_autoProgressEnabled}, cancelled={_cancellationRequested}");
                return;
            }

            // 逐渐增加进度，但不超过目标最大值（为真实完成留余量）
            if (_currentAutoProgress < _autoProgressTargetMax)
            {
                int oldProgress = _currentAutoProgress;
                _currentAutoProgress += _autoProgressIncrement;
                
                // 确保不超过目标最大值
                if (_currentAutoProgress > _autoProgressTargetMax)
                {
                    _currentAutoProgress = _autoProgressTargetMax;
                }

                Console.WriteLine($"⏲️ 自动进度更新：{oldProgress}% -> {_currentAutoProgress}% (目标:{_autoProgressTargetMax}%)");

                // 直接更新进度条，无论线程情况
                try
                {
                    if (this.InvokeRequired)
                    {
                        this.Invoke(new Action(() =>
                        {
                            progressBar.Value = _currentAutoProgress;
                            Console.WriteLine($"🖥️ 进度条UI更新完成：{_currentAutoProgress}%");
                            Application.DoEvents();
                        }));
                    }
                    else
                    {
                        progressBar.Value = _currentAutoProgress;
                        Console.WriteLine($"🖥️ 进度条UI更新完成：{_currentAutoProgress}%");
                        Application.DoEvents();
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ 进度条更新失败：{ex.Message}");
                }
            }
            else
            {
                // 到达目标最大值后减慢增长，基本停止
                Console.WriteLine($"🎯 自动进度到达目标值 {_autoProgressTargetMax}%，减慢增长");
                _autoProgressTimer.Interval = 5000; // 减慢到5秒
            }
        }

        /// <summary>
        /// 设置进度条为不确定模式
        /// </summary>
        public void SetIndeterminate(bool indeterminate)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action<bool>(SetIndeterminate), indeterminate);
                return;
            }

            progressBar.Style = indeterminate ? ProgressBarStyle.Marquee : ProgressBarStyle.Continuous;
        }

        /// <summary>
        /// 关闭窗口（线程安全）
        /// </summary>
        public void SafeClose()
        {
            try
            {
                if (this.InvokeRequired)
                {
                    this.Invoke(new Action(SafeClose));
                    return;
                }

                // 如果窗口已经关闭或正在关闭，直接返回
                if (this.IsDisposed || !this.Visible)
                    return;

                // 停止自动进度并清理Timer
                StopAutoProgress();

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"关闭进度窗口时发生错误: {ex.Message}");
                // 强制关闭
                try
                {
                    this.Hide();
                    this.Dispose();
                }
                catch
                {
                    // 忽略强制关闭时的异常
                }
            }
        }

        private void CancelButton_Click(object sender, EventArgs e)
        {
            Console.WriteLine("🚫 用户点击取消按钮，开始停止所有任务");

            _cancellationRequested = true;
            _cancellationTokenSource?.Cancel();
            cancelButton.Enabled = false;
            cancelButton.Text = "正在取消...";
            statusLabel.Text = "正在取消计算...";
            statusLabel.ForeColor = System.Drawing.Color.Red;

            // 立即停止自动进度更新
            if (_autoProgressEnabled)
            {
                _autoProgressEnabled = false;
                _autoProgressTimer?.Stop();
                Console.WriteLine("🛑 自动进度Timer已停止");
            }

            // 立即恢复主窗体的计算按钮并强制停止计算
            if (_parentForm != null)
            {
                try
                {
                    _parentForm.Invoke(new Action(() =>
                    {
                        _parentForm.RestoreCalculateButton();
                        _parentForm.ForceStopCurrentCalculation(); // 强制停止当前计算
                    }));
                    Console.WriteLine("✅ 主窗体计算按钮已恢复，计算任务已强制停止");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ 恢复计算按钮失败: {ex.Message}");
                }
            }

            // 设置一个定时器，如果2秒后还没关闭，就强制关闭（缩短等待时间）
            var forceCloseTimer = new System.Windows.Forms.Timer();
            forceCloseTimer.Interval = 2000; // 2秒
            forceCloseTimer.Tick += (s, args) =>
            {
                forceCloseTimer.Stop();
                forceCloseTimer.Dispose();

                Console.WriteLine("⏰ 强制关闭进度窗口（取消操作超时）");
                try
                {
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                }
                catch
                {
                    try
                    {
                        this.Hide();
                        this.Dispose();
                    }
                    catch
                    {
                        // 忽略异常
                    }
                }
            };
            forceCloseTimer.Start();

            Console.WriteLine("🚫 取消操作已启动，等待任务响应...");
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (!_cancellationRequested)
            {
                _cancellationRequested = true;
                _cancellationTokenSource?.Cancel();
            }
            base.OnFormClosing(e);
        }

        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                Console.WriteLine("🧹 ProgressForm正在清理资源...");

                // 确保取消标志已设置
                if (!_cancellationRequested)
                {
                    _cancellationRequested = true;
                    _cancellationTokenSource?.Cancel();
                }

                // 清理资源
                StopAutoProgress();
                _autoProgressTimer?.Dispose();
                _cancellationTokenSource?.Dispose();

                Console.WriteLine("✅ ProgressForm资源清理完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ ProgressForm资源清理时发生异常: {ex.Message}");
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }
    }
}
