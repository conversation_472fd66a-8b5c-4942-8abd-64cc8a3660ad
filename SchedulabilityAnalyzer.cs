using System;
using System.Collections.Generic;
using System.Linq;

namespace CalculateOffset
{
    /// <summary>
    /// 可调度性分析器 - 基于最坏情况响应时间(WCRT)的实时系统分析
    /// </summary>
    public class SchedulabilityAnalyzer
    {
        #region 常量定义

        /// <summary>
        /// WCRT迭代计算的最大迭代次数
        /// </summary>
        private const int MAX_WCRT_ITERATIONS = 1000;

        /// <summary>
        /// WCRT迭代收敛精度(微秒)
        /// </summary>
        private const int WCRT_CONVERGENCE_THRESHOLD = 1; // 1微秒

        // --- 主要成本权重 ---

        /// <summary>
        /// 成本函数权重 - 前置任务约束违反数权重（最高惩罚）
        /// </summary>
        private const double WEIGHT_PREREQ_VIOLATION_COUNT = 20000.0;

        /// <summary>
        /// 成本函数权重 - 前置任务约束违反量权重
        /// </summary>
        private const double WEIGHT_PREREQ_VIOLATION_AMOUNT = 50.0;

        /// <summary>
        /// 成本函数权重 - 错过截止时间的任务数权重
        /// </summary>
        private const double WEIGHT_DEADLINE_MISS_COUNT = 1000.0;

        /// <summary>
        /// 成本函数权重 - 超时总量权重
        /// </summary>
        private const double WEIGHT_TOTAL_LATENESS = 10.0;

        /// <summary>
        /// 成本函数权重 - 最大响应时间比率权重
        /// </summary>
        private const double WEIGHT_MAX_RESPONSE_RATIO = 1.0;

        /// <summary>
        /// 成本函数权重 - 不可抢占任务阻塞时间权重
        /// </summary>
        private const double WEIGHT_BLOCKING_TIME = 1.0;


        // --- 决胜局成本权重 (Tie-Breaker Weights) ---
        // 注意：这些权重必须非常小，确保只在主要成本相近时起作用，并按优先级排序

        /// <summary>
        /// Tie-Breaker权重 (第一优先级): 任务重叠/竞争惩罚权重
        /// 用于选择内部动态冲突更少的解。
        /// </summary>
        private const double WEIGHT_COMPETITION = 0.001;

        /// <summary>
        /// Tie-Breaker权重 (第四优先级): Offset均值惩罚权重
        /// 用于选择静态布局更紧凑、平均延迟更低的解。
        /// </summary>
        //private const double OFFSET_MEAN_PENALTY_WEIGHT = 0.0001;

        /// <summary>
        /// Tie-Breaker权重 (第三优先级): 优先级对齐成本权重
        /// 用于选择布局风格更符合直觉的解。
        /// </summary>
        private const double TIE_BREAKER_WEIGHT = 0.0001;

        /// <summary>
        /// Tie-Breaker权重: 静态启动重叠率惩罚权重
        /// </summary>
        private const double WEIGHT_START_OVERLAP_RATE = 0.001;

        #endregion

        #region 私有字段

        /// <summary>
        /// 时基值(微秒)
        /// </summary>
        private readonly int _timeBaseUs;

        /// <summary>
        /// 是否输出成本分解调试日志
        /// </summary>
        private const bool DEBUG_COST_LOG = false;

        /// <summary>
        /// 全局评估缓存实例（静态，所有分析器实例共享）
        /// </summary>
        private static readonly EvaluationCache _globalEvaluationCache = new EvaluationCache(
            maxCacheSize: 1000000,    // 最大缓存100万个解
            maxCacheAgeMinutes: 30,   // 缓存30分钟后过期
            enableStatistics: true    // 启用统计
        );

        /// <summary>
        /// 是否启用评估缓存
        /// </summary>
        private readonly bool _enableCache;

        #endregion

        #region 构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        /// <param name="enableCache">是否启用评估缓存</param>
        public SchedulabilityAnalyzer(int timeBaseUs, bool enableCache = true)
        {
            _timeBaseUs = timeBaseUs;
            _enableCache = enableCache;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 分析任务集的可调度性
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>分析结果</returns>
        public SchedulabilityResult AnalyzeSchedulability(List<TaskItem> tasks)
        {
            try
            {
                // 过滤有效任务并验证
                var validTasks = ValidateAndPrepareTasks(tasks);
                if (validTasks.Count == 0)
                {
                    return new SchedulabilityResult
                    {
                        IsValid = false,
                        ErrorMessage = "没有有效的任务可分析"
                    };
                }

                // 🚀 尝试从缓存获取结果
                if (_enableCache)
                {
                    var solutionKey = new SolutionKey(validTasks);
                    if (_globalEvaluationCache.TryGetCachedResult(solutionKey, out CachedEvaluationResult cachedResult))
                    {
                        // 缓存命中，直接返回缓存的结果
                        return cachedResult.SchedulabilityResult;
                    }
                }

                // 按优先级排序任务(优先级高的在前)
                var sortedTasks = validTasks.OrderByDescending(t => t.Priority ?? 0)
                                           .ThenBy(t => t.PeriodUs ?? int.MaxValue)
                                           .ToList();

                // 计算每个任务的WCRT
                var taskAnalysisResults = new List<TaskAnalysisResult>();
                bool allTasksSchedulable = true;

                foreach (var task in sortedTasks)
                {
                    var taskResult = CalculateWorstCaseResponseTime(task, sortedTasks);
                    taskAnalysisResults.Add(taskResult);

                    if (!taskResult.MeetsDeadline)
                    {
                        allTasksSchedulable = false;
                    }
                }

                // 新增：检查前置任务时间约束
                var (constraintsValid, violationCount, maxViolationAmount) = CheckPrerequisiteTimeConstraints(sortedTasks);

                // 计算成本函数，将约束违反情况传进去
                double costValue = CalculateCostFunction(taskAnalysisResults, violationCount, maxViolationAmount);

                // 计算统计信息
                var statistics = CalculateStatistics(taskAnalysisResults, validTasks);

                // CPU利用率检查：这是不可调度性的基本条件
                if (statistics.CpuUsage > 100.0)
                {
                    allTasksSchedulable = false;
                }

                // 如果约束无效，则整个结果也应标记为不可调度
                if (!constraintsValid)
                {
                    allTasksSchedulable = false;
                }

                // 构建最终结果
                var result = new SchedulabilityResult
                {
                    IsValid = true,
                    IsSchedulable = allTasksSchedulable,
                    TaskResults = taskAnalysisResults,
                    CostValue = costValue,
                    Statistics = statistics,
                    ErrorMessage = allTasksSchedulable ? "" : GetSchedulabilityErrorMessage(statistics.CpuUsage, constraintsValid)
                };

                // 🚀 将结果添加到缓存
                if (_enableCache)
                {
                    var solutionKey = new SolutionKey(validTasks);
                    _globalEvaluationCache.AddToCache(solutionKey, costValue, result);
                }

                return result;
            }
            catch (Exception ex)
            {
                return new SchedulabilityResult
                {
                    IsValid = false,
                    ErrorMessage = $"分析过程出错: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 清空评估缓存
        /// </summary>
        public static void ClearEvaluationCache()
        {
            _globalEvaluationCache.Clear();
        }

        /// <summary>
        /// 获取评估缓存统计信息
        /// </summary>
        /// <returns>缓存统计</returns>
        public static CacheStatistics GetCacheStatistics()
        {
            return _globalEvaluationCache.GetStatistics();
        }

        /// <summary>
        /// 打印评估缓存统计报告
        /// </summary>
        public static void PrintCacheStatistics()
        {
            _globalEvaluationCache.PrintStatistics();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 验证并准备任务数据
        /// </summary>
        /// <param name="tasks">原始任务列表</param>
        /// <returns>有效任务列表</returns>
        private List<TaskItem> ValidateAndPrepareTasks(List<TaskItem> tasks)
        {
            return tasks.Where(t =>
                t.Enabled &&
                !string.IsNullOrWhiteSpace(t.TaskName) &&
                t.PeriodUs.HasValue && t.PeriodUs.Value > 0 &&
                t.OffsetUs.HasValue && t.OffsetUs.Value >= 0 &&
                t.ExecutionTimeUs.HasValue && t.ExecutionTimeUs.Value > 0
            ).ToList();
        }

        /// <summary>
        /// 计算单个任务的最坏情况响应时间
        /// </summary>
        /// <param name="task">目标任务</param>
        /// <param name="allTasks">所有任务列表(按优先级排序)</param>
        /// <returns>任务分析结果</returns>
        private TaskAnalysisResult CalculateWorstCaseResponseTime(TaskItem task, List<TaskItem> allTasks)
        {
            // 直接使用微秒属性
            int taskExecutionTimeUs = task.ExecutionTimeUs.Value;
            int taskPeriodUs = task.PeriodUs.Value;
            int taskOffsetUs = task.OffsetUs.Value;
            int taskDeadlineUs = taskPeriodUs; // 假设截止时间等于周期
            
            // 新增：计算任务i的最大阻塞时间 B_i
            int blockingTimeUs = CalculateBlockingTime(task, allTasks);

            // 获取比当前任务优先级高的任务
            var higherPriorityTasks = GetHigherPriorityTasks(task, allTasks);

            // 🔧 优化：使用改进的迭代方法计算WCRT，包含动态jitter估算
            // 初始WCRT = 基础执行时间 + 阻塞时间 + 启动jitter
            int currentWcrt = taskExecutionTimeUs + blockingTimeUs + task.JitterTimeUs; // 启动时的一次jitter
            int previousWcrt = 0;
            int iterationCount = 0;

            // 添加早期检测：如果没有阻塞且没有高优先级任务，只需考虑启动jitter
            if (blockingTimeUs == 0 && higherPriorityTasks.Count == 0)
            {
                currentWcrt = taskExecutionTimeUs + task.JitterTimeUs; // 只有启动jitter
            }
            else
            {
                while (Math.Abs(currentWcrt - previousWcrt) > WCRT_CONVERGENCE_THRESHOLD &&
                       iterationCount < MAX_WCRT_ITERATIONS)
                {
                    previousWcrt = currentWcrt;

                    // 计算高优先级任务的干扰（不包含jitter）
                    int totalInterference = CalculateInterferenceWithoutJitter(task, higherPriorityTasks, currentWcrt);
                    
                    // 估算抢占次数并计算相应的jitter开销
                    int estimatedPreemptions = EstimatePreemptionCount(task, higherPriorityTasks, currentWcrt);
                    int jitterOverhead = task.JitterTimeUs * (1 + estimatedPreemptions); // 启动1次 + 抢占恢复n次

                    // 更新WCRT: R_i = C_i + B_i + I_i + Jitter_overhead
                    int newWcrt = taskExecutionTimeUs + blockingTimeUs + totalInterference + jitterOverhead;

                    // 🔧 添加单调性检查，防止响应时间异常跳跃
                    if (newWcrt < previousWcrt)
                    {
                        // 如果新的WCRT比前一次小，说明可能有计算错误，取较大值
                        currentWcrt = Math.Max(newWcrt, taskExecutionTimeUs + blockingTimeUs + task.JitterTimeUs);
                    }
                    else
                    {
                        currentWcrt = newWcrt;
                    }

                    iterationCount++;

                    // 🔧 添加合理性检查：如果WCRT超过任务周期太多，可能不可调度
                    if (currentWcrt > taskDeadlineUs * 10)
                    {
                        currentWcrt = int.MaxValue;
                        break;
                    }
                }
            }

            // 检查是否收敛
            bool converged = iterationCount < MAX_WCRT_ITERATIONS;
            if (!converged)
            {
                // 如果未收敛，可能存在不可调度的情况
                currentWcrt = int.MaxValue;
            }

            // 创建分析结果
            var result = new TaskAnalysisResult
            {
                TaskName = task.TaskName,
                TaskIndex = task.TaskIndex,
                Priority = task.Priority ?? 0,
                PeriodUs = taskPeriodUs,
                ExecutionTimeUs = taskExecutionTimeUs,
                OffsetUs = taskOffsetUs,
                DeadlineUs = taskDeadlineUs,
                WcrtUs = currentWcrt,
                BlockingTimeUs = blockingTimeUs, // 新增：阻塞时间
                MeetsDeadline = converged && (currentWcrt <= taskDeadlineUs),
                ResponseTimeRatio = converged ? (double)currentWcrt / taskDeadlineUs : double.MaxValue,
                LatenessUs = Math.Max(0, currentWcrt - taskDeadlineUs),
                IterationCount = iterationCount,
                Converged = converged
            };

            return result;
        }

        /// <summary>
        /// 获取比指定任务优先级高的任务列表
        /// </summary>
        /// <param name="task">目标任务</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <returns>高优先级任务列表</returns>
        private List<TaskItem> GetHigherPriorityTasks(TaskItem task, List<TaskItem> allTasks)
        {
            int taskPriority = task.Priority ?? 0;
            return allTasks.Where(t =>
                !t.TaskName.Equals(task.TaskName, StringComparison.OrdinalIgnoreCase) &&
                (t.Priority ?? 0) > taskPriority
            ).ToList();
        }

        /// <summary>
        /// 改进版：计算任务i在实际时间线上可能遭受的真实阻塞时间 (B_i)
        /// 基于时间窗口重叠分析，而非简单的优先级比较。
        /// </summary>
        /// <param name="task">目标任务</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <returns>实际阻塞时间（微秒）</returns>
        private int CalculateBlockingTime(TaskItem task, List<TaskItem> allTasks)
        {
            // 任务自身属性检查，如果无效则无阻塞
            if (!task.OffsetUs.HasValue || !task.ExecutionTimeUs.HasValue || !task.PeriodUs.HasValue)
            {
                return 0;
            }

            int taskPriority = task.Priority ?? 0;
            int taskPeriodUs = task.PeriodUs.Value;

            // 1. 找出所有可能造成阻塞的源头：优先级低于目标任务，并且是不可抢占的任务
            var lowerPriorityNonPreemptiveTasks = allTasks.Where(t =>
                !t.TaskName.Equals(task.TaskName, StringComparison.OrdinalIgnoreCase) &&
                (t.Priority ?? 0) < taskPriority &&
                t.NonPreemptive &&
                t.OffsetUs.HasValue &&
                t.ExecutionTimeUs.HasValue &&
                t.PeriodUs.HasValue
            ).ToList();

            // 如果没有这样的任务，则阻塞时间为0
            if (!lowerPriorityNonPreemptiveTasks.Any())
            {
                return 0;
            }

            int maxBlockingTime = 0;

            // 2. 对每一个潜在的阻塞源，进行轻量级的重叠分析
            foreach (var lpTask in lowerPriorityNonPreemptiveTasks)
            {
                int lpOffsetUs = lpTask.OffsetUs.Value;
                int lpExecutionUs = lpTask.ExecutionTimeUs.Value;
                int lpPeriodUs = lpTask.PeriodUs.Value;

                // 3. 计算一个足够大的分析窗口，通常是两个任务周期的最小公倍数（超周期）
                //    为了性能，我们限制这个窗口的大小，避免计算量爆炸。
                int analysisWindowUs = TaskUtility.Lcm(taskPeriodUs, lpPeriodUs);
                const int maxAnalysisWindow = 2000000; // 限制最大分析窗口为2000ms，防止过大
                if (analysisWindowUs > maxAnalysisWindow)
                {
                    analysisWindowUs = maxAnalysisWindow;
                }

                int currentMaxBlockingFromThisLpTask = 0;

                // 4. 在分析窗口内，检查目标任务的每一次激活，是否会与低优先级任务的执行窗口发生"阻塞式"重叠
                for (int taskActivationTime = task.OffsetUs.Value;
                     taskActivationTime < analysisWindowUs;
                     taskActivationTime += taskPeriodUs)
                {
                    // 5. 找到在 taskActivationTime 附近，lpTask 的执行窗口
                    //    我们需要找到在 taskActivationTime 之前开始，并可能持续到 taskActivationTime 之后的那个 lpTask 实例

                    // 计算在 taskActivationTime 之前，lpTask 最近一次的激活时间
                    int numPeriodsBefore = (taskActivationTime - lpOffsetUs) / lpPeriodUs;
                    if (taskActivationTime < lpOffsetUs)
                    {
                        // 如果目标任务激活点在lpTask第一次激活之前，需要特殊处理
                        // 但为了简化，我们假设这种情况不会发生或影响不大，或者可以设为0
                        numPeriodsBefore = -1;
                    }

                    if (numPeriodsBefore >= 0)
                    {
                        int lpInstanceStartTime = lpOffsetUs + numPeriodsBefore * lpPeriodUs;
                        int lpInstanceEndTime = lpInstanceStartTime + lpExecutionUs;

                        // 6. 核心判断：是否发生阻塞？
                        //    阻塞发生的条件是：低优先级任务在目标任务激活时(taskActivationTime)仍在运行。
                        //    即：lpInstanceStartTime < taskActivationTime < lpInstanceEndTime
                        if (lpInstanceStartTime < taskActivationTime && taskActivationTime < lpInstanceEndTime)
                        {
                            // 7. 如果发生阻塞，计算实际的阻塞时长
                            int actualBlockingDuration = lpInstanceEndTime - taskActivationTime;

                            // 更新这个低优先级任务能造成的最大阻塞
                            if (actualBlockingDuration > currentMaxBlockingFromThisLpTask)
                            {
                                currentMaxBlockingFromThisLpTask = actualBlockingDuration;
                            }
                        }
                    }
                }

                // 8. 更新全局的最大阻塞时间（来自所有低优先级任务）
                if (currentMaxBlockingFromThisLpTask > maxBlockingTime)
                {
                    maxBlockingTime = currentMaxBlockingFromThisLpTask;
                }
            }

            return maxBlockingTime;
        }



        /// <summary>
        /// 计算高优先级任务对目标任务的干扰
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="higherPriorityTasks">高优先级任务列表</param>
        /// <param name="responseTime">当前响应时间估计值</param>
        /// <returns>总干扰时间(微秒)</returns>
        private int CalculateInterference(TaskItem targetTask, List<TaskItem> higherPriorityTasks, int responseTime)
        {
            int totalInterference = 0;
            int targetOffsetUs = targetTask.OffsetUs.Value;

            foreach (var hpTask in higherPriorityTasks)
            {
                int hpPeriodUs = hpTask.PeriodUs.Value;
                int hpExecutionTimeUs = hpTask.ExecutionTimeUs.Value;
                int hpOffsetUs = hpTask.OffsetUs.Value;
                
                // 计算高优先级任务的有效执行时间（包含jitter）
                int hpEffectiveExecutionTimeUs = hpExecutionTimeUs + hpTask.JitterTimeUs;

                // 计算考虑Offset的干扰次数
                int interferenceCount = CalculateInterferenceWithOffset(
                    targetOffsetUs, responseTime, hpOffsetUs, hpPeriodUs);

                totalInterference += interferenceCount * hpEffectiveExecutionTimeUs;
            }

            return totalInterference;
        }

        /// <summary>
        /// 计算高优先级任务的干扰时间（不包含jitter，用于新的jitter计算方式）
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="higherPriorityTasks">高优先级任务列表</param>
        /// <param name="responseTime">当前响应时间估计值</param>
        /// <returns>总干扰时间(微秒，不含jitter)</returns>
        private int CalculateInterferenceWithoutJitter(TaskItem targetTask, List<TaskItem> higherPriorityTasks, int responseTime)
        {
            int totalInterference = 0;
            int targetOffsetUs = targetTask.OffsetUs.Value;

            foreach (var hpTask in higherPriorityTasks)
            {
                int hpPeriodUs = hpTask.PeriodUs.Value;
                int hpExecutionTimeUs = hpTask.ExecutionTimeUs.Value; // 不包含jitter的原始执行时间
                int hpOffsetUs = hpTask.OffsetUs.Value;

                // 计算考虑Offset的干扰次数
                int interferenceCount = CalculateInterferenceWithOffset(
                    targetOffsetUs, responseTime, hpOffsetUs, hpPeriodUs);

                totalInterference += interferenceCount * hpExecutionTimeUs;
            }

            return totalInterference;
        }

        /// <summary>
        /// 估算目标任务可能被抢占的次数
        /// </summary>
        /// <param name="targetTask">目标任务</param>
        /// <param name="higherPriorityTasks">高优先级任务列表</param>
        /// <param name="responseTime">当前响应时间估计值</param>
        /// <returns>估算的抢占次数</returns>
        private int EstimatePreemptionCount(TaskItem targetTask, List<TaskItem> higherPriorityTasks, int responseTime)
        {
            int totalPreemptions = 0;
            int targetOffsetUs = targetTask.OffsetUs.Value;

            foreach (var hpTask in higherPriorityTasks)
            {
                int hpPeriodUs = hpTask.PeriodUs.Value;
                int hpOffsetUs = hpTask.OffsetUs.Value;

                // 估算高优先级任务在目标任务响应时间窗口内的激活次数
                // 每次激活都可能导致一次抢占
                int activationCount = CalculateInterferenceWithOffset(
                    targetOffsetUs, responseTime, hpOffsetUs, hpPeriodUs);

                totalPreemptions += activationCount;
            }

            return totalPreemptions;
        }

        /// <summary>
        /// 计算考虑Offset的干扰次数
        /// 这是对经典RMA公式的扩展，考虑任务偏移量的影响
        /// </summary>
        /// <param name="targetOffsetUs">目标任务偏移量</param>
        /// <param name="responseTimeUs">目标任务响应时间</param>
        /// <param name="hpOffsetUs">高优先级任务偏移量</param>
        /// <param name="hpPeriodUs">高优先级任务周期</param>
        /// <returns>干扰次数</returns>
        private int CalculateInterferenceWithOffset(int targetOffsetUs, int responseTimeUs,
            int hpOffsetUs, int hpPeriodUs)
        {
            // 目标任务的分析窗口: [targetOffset, targetOffset + responseTime)
            int windowStart = targetOffsetUs;
            int windowEnd = targetOffsetUs + responseTimeUs;

            // 计算高优先级任务在分析窗口内的激活次数
            int activationCount = 0;

            // 找到第一个可能影响的高优先级任务激活时间点
            int firstActivation = hpOffsetUs;
            if (firstActivation < windowStart)
            {
                // 计算第一个在窗口内或之后的激活时间
                int periodsToSkip = (windowStart - hpOffsetUs + hpPeriodUs - 1) / hpPeriodUs;
                firstActivation = hpOffsetUs + periodsToSkip * hpPeriodUs;
            }

            // 计算所有在分析窗口内的激活
            for (int activation = firstActivation; activation < windowEnd; activation += hpPeriodUs)
            {
                activationCount++;

                // 防止无限循环
                if (activationCount > 10000)
                {
                    break;
                }
            }

            return activationCount;
        }

        /// <summary>
        /// 计算成本函数值
        /// </summary>
        /// <param name="taskResults">任务分析结果列表</param>
        /// <param name="violationCount">约束违反次数</param>
        /// <param name="maxViolationAmount">最大约束违反量</param>
        /// <returns>成本值</returns>
        private double CalculateCostFunction(List<TaskAnalysisResult> taskResults, int violationCount, int maxViolationAmount)
        {
            // --- 主要成本计算 ---

            // 1. 前置任务约束惩罚 (最高优先级)
            double prereqPenalty = (WEIGHT_PREREQ_VIOLATION_COUNT * violationCount) +
                                   (WEIGHT_PREREQ_VIOLATION_AMOUNT * TaskUtility.ConvertToMilliseconds(maxViolationAmount));

            // 2. 调度性与性能成本
            int deadlineMissCount = taskResults.Count(r => !r.MeetsDeadline);
            int totalLatenessUs = taskResults.Sum(r => r.LatenessUs);
            double maxResponseRatio = taskResults.Max(r => r.ResponseTimeRatio);
            if (double.IsInfinity(maxResponseRatio) || double.IsNaN(maxResponseRatio))
            {
                maxResponseRatio = 1000.0; // 不可调度的情况赋予极大值
            }
            double blockingCost = taskResults
                .Where(r => r.BlockingTimeUs > 0)
                .Sum(r => WEIGHT_BLOCKING_TIME * TaskUtility.ConvertToMilliseconds(r.BlockingTimeUs));

            double deadlineCost = WEIGHT_DEADLINE_MISS_COUNT * deadlineMissCount;
            double latenessCost = WEIGHT_TOTAL_LATENESS * TaskUtility.ConvertToMilliseconds(totalLatenessUs);
            double responseCost = WEIGHT_MAX_RESPONSE_RATIO * maxResponseRatio;

            // --- 决胜局成本计算 (Tie-Breakers) ---

            // 3. 任务重叠/竞争成本 (决胜局第一优先级)
            double totalCompetitionMs = 0;
            foreach (var result in taskResults)
            {
                if (result.Converged)
                {
                    long interferenceUs = result.WcrtUs - result.ExecutionTimeUs - result.BlockingTimeUs;
                    long competitionUs = Math.Max(0, result.BlockingTimeUs + interferenceUs);
                    totalCompetitionMs += TaskUtility.ConvertToMilliseconds((int)competitionUs);
                }
            }
            double competitionPenalty = totalCompetitionMs * WEIGHT_COMPETITION;

            // 4. Offset均值成本 (决胜局第二优先级)
            //double offsetMeanPenalty = CalculateOffsetMeanPenalty(taskResults);

            // 5. 优先级对齐成本 (已移至统一挑选阶段，不再计入成本函数)
            double alignmentCost = 0.0; //CalculatePriorityAlignmentCost(taskResults);

#if false
            // 6. 静态启动重叠率成本（极小权重，仅在主成本相同时起到偏好作用）
            double startOverlapRate = CalculateStartOverlapRate(taskResults);
            double startOverlapPenalty = WEIGHT_START_OVERLAP_RATE * startOverlapRate;

            // 7. 轻量级代理指标：时间线负载方差（仅作为tie-breaker，极小权重，避免重型动态模拟）
            //    注意：此处只在统计时使用任务Offset/Period/Exec构造一个轻量时间线，不改变WCRT主逻辑
            double timelineLoadVariancePenalty = 0.0;
            try
            {
                var tasksForVariance = taskResults.Select(r => new TaskItem
                {
                    TaskIndex = r.TaskIndex,
                    TaskName = r.TaskName,
                    PeriodUs = r.PeriodUs,
                    ExecutionTimeUs = r.ExecutionTimeUs,
                    OffsetUs = r.OffsetUs,
                    Enabled = true,
                    MaxOffsetUs = r.OffsetUs ?? 0  // 为一致性设置MaxOffsetUs
                }).ToList();

                // 使用分析器的时基；限制窗口由工具函数内部控制
                double variance = TaskUtility.ComputeTimelineLoadVariance(tasksForVariance, _timeBaseUs);

                // 归一化/缩放：将方差转换为极小的惩罚项，避免影响主目标
                const double TIMELINE_VARIANCE_WEIGHT = 0.0005; // 小权重
                timelineLoadVariancePenalty = variance * TIMELINE_VARIANCE_WEIGHT;
            }
            catch { }
#endif

            // --- 综合总成本 ---
            double cost = prereqPenalty +
                          deadlineCost +
                          latenessCost +
                          responseCost +
                          blockingCost +
                          competitionPenalty;// +
                          //offsetMeanPenalty +
                          //alignmentCost +
                          //startOverlapPenalty +
                          //timelineLoadVariancePenalty;

            // 成本计算完成，调试日志已禁用

            return cost;
        }

        /// <summary>
        /// 计算优先级对齐成本 - 作为一个次要的、打破僵局的激励
        /// </summary>
        /// <param name="taskResults">任务分析结果列表</param>
        /// <returns>一个很小的成本值</returns>
        private double CalculatePriorityAlignmentCost(List<TaskAnalysisResult> taskResults)
        {
            double alignmentCost = 0.0;

            var sortedByPriority = taskResults.OrderByDescending(r => r.Priority).ToList();

            for (int i = 0; i < sortedByPriority.Count - 1; i++)
            {
                var higherTask = sortedByPriority[i];
                var lowerTask = sortedByPriority[i + 1];

                // 如果高优先级任务的Offset大于低优先级任务的Offset
                if (higherTask.OffsetUs > lowerTask.OffsetUs)
                {
                    // 施加一个与Offset差值和权重相关的轻微惩罚
                    int offsetDifferenceUs = higherTask.OffsetUs - lowerTask.OffsetUs;
                    alignmentCost += TaskUtility.ConvertToMilliseconds(offsetDifferenceUs) * TIE_BREAKER_WEIGHT;
                }
            }
            return alignmentCost;
        }

        /// <summary>
        /// 计算静态启动重叠率：任务在其启动时刻，是否有其他任务正在执行的比例
        /// 仅用于成本等价时的轻微偏好
        /// </summary>
        /// <param name="taskResults">任务分析结果列表</param>
        /// <returns>静态启动重叠率(0-1)</returns>
        private double CalculateStartOverlapRate(List<TaskAnalysisResult> taskResults)
        {
            if (taskResults == null || taskResults.Count == 0)
            {
                return 0.0;
            }

            // 构建有效任务列表
            var valid = taskResults
                .Where(r => r.PeriodUs > 0 && r.ExecutionTimeUs > 0)
                .Select(r => new { r.TaskIndex, r.TaskName, PeriodUs = r.PeriodUs, ExecUs = r.ExecutionTimeUs, OffsetUs = r.OffsetUs })
                .ToList();

            if (valid.Count == 0)
            {
                return 0.0;
            }

            // 计算超周期(LCM)，并限制最大分析窗口避免极端大数
            int analysisWindowUs = valid[0].PeriodUs;
            for (int k = 1; k < valid.Count; k++)
            {
                analysisWindowUs = TaskUtility.Lcm(analysisWindowUs, valid[k].PeriodUs);
            }
            const int MAX_ANALYSIS_WINDOW_US = 2000000; // 2000ms上限
            if (analysisWindowUs > MAX_ANALYSIS_WINDOW_US)
            {
                analysisWindowUs = MAX_ANALYSIS_WINDOW_US;
            }

            int totalStarts = 0;
            int overlappedStarts = 0;

            foreach (var ti in valid)
            {
                int periodI = ti.PeriodUs;
                if (periodI <= 0)
                {
                    continue;
                }

                // 将首个启动点标准化到[0, period)区间
                int firstStart = ti.OffsetUs % periodI;
                if (firstStart < 0)
                {
                    firstStart += periodI;
                }

                for (int start = firstStart; start < analysisWindowUs; start += periodI)
                {
                    totalStarts++;
                    bool isOverlapped = false;

                    foreach (var tj in valid)
                    {
                        if (tj.TaskIndex == ti.TaskIndex)
                        {
                            continue;
                        }

                        int periodJ = tj.PeriodUs;
                        int execJ = tj.ExecUs;
                        int offsetJ = tj.OffsetUs;
                        if (periodJ <= 0 || execJ <= 0)
                        {
                            continue;
                        }

                        int rel = start - offsetJ;
                        int mod = rel % periodJ;
                        if (mod < 0)
                        {
                            mod += periodJ;
                        }
                        if (mod < execJ)
                        {
                            isOverlapped = true;
                            break;
                        }
                    }

                    if (isOverlapped)
                    {
                        overlappedStarts++;
                    }
                }
            }

            if (totalStarts == 0)
            {
                return 0.0;
            }

            return (double)overlappedStarts / totalStarts;
        }

        /// <summary>
        /// 计算Offset均值惩罚 - 引导算法在成本相同时选择Offset均值更小的解
        /// </summary>
        /// <param name="taskResults">任务分析结果列表</param>
        /// <returns>基于Offset均值的微小惩罚值</returns>
        //private double CalculateOffsetMeanPenalty(List<TaskAnalysisResult> taskResults)
        //{
        //    if (taskResults.Count == 0) return 0.0;

        //    // 计算所有任务的Offset均值（毫秒单位）
        //    double offsetMeanMs = taskResults.Average(r => TaskUtility.ConvertToMilliseconds(r.OffsetUs));

        //    // 返回基于均值的微小惩罚：均值越大，惩罚越大
        //    // 使用非常小的权重，确保只在主要成本相同时起作用
        //    double penalty = offsetMeanMs * OFFSET_MEAN_PENALTY_WEIGHT;

        //    return penalty;
        //}

        /// <summary>
        /// 计算统计信息
        /// </summary>
        /// <param name="taskResults">任务分析结果列表</param>
        /// <param name="validTasks">有效任务列表</param>
        /// <returns>统计信息</returns>
        private SchedulabilityStatistics CalculateStatistics(List<TaskAnalysisResult> taskResults, List<TaskItem> validTasks)
        {
            var validResults = taskResults.Where(r => r.Converged && r.ResponseTimeRatio < 1000.0).ToList();

            // 计算CPU利用率
            double cpuUsage = CalculateCpuUtilization(validTasks);

            return new SchedulabilityStatistics
            {
                TotalTasks = taskResults.Count,
                SchedulableTasks = taskResults.Count(r => r.MeetsDeadline),
                DeadlineMissCount = taskResults.Count(r => !r.MeetsDeadline),
                MaxResponseTimeRatio = validResults.Any() ? validResults.Max(r => r.ResponseTimeRatio) : 0,
                AverageResponseTimeRatio = validResults.Any() ? validResults.Average(r => r.ResponseTimeRatio) : 0,
                ResponseTimeRatioVariance = validResults.Count > 1 ?
                    CalculateVariance(validResults.Select(r => r.ResponseTimeRatio)) : 0,
                TotalLatenessMs = TaskUtility.ConvertToMilliseconds(taskResults.Sum(r => r.LatenessUs)),
                MaxOffsetUs = taskResults.Max(r => r.OffsetUs),
                CpuUsage = cpuUsage
            };
        }

        /// <summary>
        /// 计算CPU利用率（包含基于抢占估算的任务切换开销）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>CPU利用率(百分比)</returns>
        private double CalculateCpuUtilization(List<TaskItem> tasks)
        {
            if (tasks.Count == 0) return 0;

            // 按优先级排序任务（用于抢占分析）
            var sortedTasks = TaskUtility.SortTasksByPriorityAndPeriod(tasks);
            double totalUtilization = 0;
            
            foreach (var task in sortedTasks)
            {
                if (task.ExecutionTimeUs.HasValue && task.PeriodUs.HasValue && task.PeriodUs.Value > 0)
                {
                    // 获取比当前任务优先级高的任务
                    var higherPriorityTasks = GetHigherPriorityTasks(task, sortedTasks);
                    
                    // 使用更保守但合理的jitter估算：在CPU利用率计算中，先假设最少的抢占
                    // 对于CPU利用率估算，我们采用更乐观的估算避免过度悲观
                    int estimatedPreemptions = 0;
                    
                    // 只有当高优先级任务的offset与当前任务可能重叠时，才计算抢占
                    foreach (var hpTask in higherPriorityTasks)
                    {
                        int targetOffsetUs = task.OffsetUs.Value;
                        int hpOffsetUs = hpTask.OffsetUs.Value;
                        int hpExecutionUs = hpTask.ExecutionTimeUs.Value;
                        int taskExecutionUs = task.ExecutionTimeUs.Value;
                        
                        // 简化的重叠检查：如果高优先级任务的执行时间窗口与当前任务重叠
                        int hpStartUs = hpOffsetUs;
                        int hpEndUs = hpOffsetUs + hpExecutionUs;
                        int taskStartUs = targetOffsetUs;
                        int taskEndUs = targetOffsetUs + taskExecutionUs;
                        
                        // 检查在一个周期内是否重叠
                        if (Math.Max(hpStartUs, taskStartUs) < Math.Min(hpEndUs, taskEndUs))
                        {
                            estimatedPreemptions++;
                        }
                    }
                    
                    // 计算jitter开销：启动1次 + 实际可能的抢占恢复次数
                    int jitterOverhead = task.JitterTimeUs * (1 + estimatedPreemptions);
                    
                    // 实际CPU占用 = 执行时间 + jitter开销
                    int effectiveExecutionTimeUs = task.ExecutionTimeUs.Value + jitterOverhead;
                    totalUtilization += (double)effectiveExecutionTimeUs / task.PeriodUs.Value;
                    
                    // CPU利用率计算完成
                }
            }

            return totalUtilization * 100; // 转换为百分比
        }

        /// <summary>
        /// 生成可调度性错误消息
        /// </summary>
        /// <param name="cpuUsage">CPU利用率</param>
        /// <param name="constraintsValid">约束是否有效</param>
        /// <returns>错误消息</returns>
        private string GetSchedulabilityErrorMessage(double cpuUsage, bool constraintsValid)
        {
            var reasons = new List<string>();

            // 1. CPU利用率检查（最高优先级）
            if (cpuUsage > 100.0)
            {
                reasons.Add($"CPU利用率过高({cpuUsage:F1}%)");
            }

            // 2. 前置任务约束检查
            if (!constraintsValid)
            {
                reasons.Add("前置任务约束违反");
            }

            // 3. 截止时间检查
            // 这里不需要单独检查，因为CPU利用率超标时必然有任务错过截止时间
            if (cpuUsage <= 100.0)
            {
                reasons.Add("存在任务错过截止时间");
            }

            return string.Join("，", reasons);
        }

        /// <summary>
        /// 计算方差
        /// </summary>
        /// <param name="values">数值序列</param>
        /// <returns>方差</returns>
        private double CalculateVariance(IEnumerable<double> values)
        {
            var valueList = values.ToList();
            if (valueList.Count <= 1) return 0;

            double mean = valueList.Average();
            return valueList.Select(v => Math.Pow(v - mean, 2)).Average();
        }

        /// <summary>
        /// 检查前置任务时间约束
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>约束有效性, 约束违反次数, 最大约束违反量（微秒）</returns>
        private (bool isValid, int violationCount, int maxViolationAmount) CheckPrerequisiteTimeConstraints(List<TaskItem> tasks)
        {
            int violationCount = 0;
            int maxViolationAmount = 0;
            var taskMap = tasks.ToDictionary(t => t.TaskName.Trim(), StringComparer.OrdinalIgnoreCase);

            foreach (var task in tasks)
            {
                if (string.IsNullOrWhiteSpace(task.PrerequisiteTasks) || !task.OffsetUs.HasValue) continue;

                int taskStartUs = task.OffsetUs.Value;
                var prerequisiteNames = TaskValidator.ParsePrerequisiteTaskNames(task.PrerequisiteTasks);

                foreach (var name in prerequisiteNames)
                {
                    if (taskMap.TryGetValue(name, out var prereqTask) &&
                        prereqTask.OffsetUs.HasValue &&
                        prereqTask.ExecutionTimeUs.HasValue)
                    {
                        int prereqEndUs = prereqTask.OffsetUs.Value + prereqTask.ExecutionTimeUs.Value;
                        if (taskStartUs < prereqEndUs)
                        {
                            violationCount++;
                            maxViolationAmount = Math.Max(maxViolationAmount, prereqEndUs - taskStartUs);
                        }
                    }
                }
            }
            return (violationCount == 0, violationCount, maxViolationAmount);
        }

        /// <summary>
        /// 公开版：基于任务列表计算静态启动重叠率（用于调试/比较）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>静态启动重叠率(0-1)</returns>
        public static double ComputeStartOverlapRateForTasks(List<TaskItem> tasks)
        {
            if (tasks == null || tasks.Count == 0)
            {
                return 0.0;
            }

            // 过滤基本有效的任务
            var valid = tasks.Where(t => t != null && t.Enabled &&
                                         t.OffsetUs.HasValue && t.ExecutionTimeUs.HasValue && t.PeriodUs.HasValue &&
                                         t.ExecutionTimeUs.Value > 0 && t.PeriodUs.Value > 0)
                              .Select(t => new { t.TaskIndex, t.TaskName, OffsetUs = t.OffsetUs.Value, ExecUs = t.ExecutionTimeUs.Value, PeriodUs = t.PeriodUs.Value })
                              .ToList();

            if (valid.Count == 0)
            {
                return 0.0;
            }

            // 计算超周期(LCM)，并限制最大分析窗口避免极端大数
            int analysisWindowUs = valid[0].PeriodUs;
            for (int k = 1; k < valid.Count; k++)
            {
                analysisWindowUs = TaskUtility.Lcm(analysisWindowUs, valid[k].PeriodUs);
            }
            const int MAX_ANALYSIS_WINDOW_US = 2000000; // 2000ms上限
            if (analysisWindowUs > MAX_ANALYSIS_WINDOW_US)
            {
                analysisWindowUs = MAX_ANALYSIS_WINDOW_US;
            }

            int totalStarts = 0;
            int overlappedStarts = 0;

            foreach (var ti in valid)
            {
                int periodI = ti.PeriodUs;
                if (periodI <= 0)
                {
                    continue;
                }

                // 标准化首启动点到[0, period)
                int firstStart = ti.OffsetUs % periodI;
                if (firstStart < 0)
                {
                    firstStart += periodI;
                }

                for (int start = firstStart; start < analysisWindowUs; start += periodI)
                {
                    totalStarts++;
                    bool isOverlapped = false;

                    foreach (var tj in valid)
                    {
                        if (tj.TaskIndex == ti.TaskIndex)
                        {
                            continue;
                        }

                        int periodJ = tj.PeriodUs;
                        int execJ = tj.ExecUs;
                        int offsetJ = tj.OffsetUs;
                        if (periodJ <= 0 || execJ <= 0)
                        {
                            continue;
                        }

                        int rel = start - offsetJ;
                        int mod = rel % periodJ;
                        if (mod < 0)
                        {
                            mod += periodJ;
                        }
                        if (mod < execJ)
                        {
                            isOverlapped = true;
                            break;
                        }
                    }

                    if (isOverlapped)
                    {
                        overlappedStarts++;
                    }
                }
            }

            if (totalStarts == 0)
            {
                return 0.0;
            }

            return (double)overlappedStarts / totalStarts;
        }

#endregion
    }

    #region 结果数据结构

    /// <summary>
    /// 可调度性分析结果
    /// </summary>
    public class SchedulabilityResult
    {
        /// <summary>
        /// 分析是否有效
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// 任务集是否可调度
        /// </summary>
        public bool IsSchedulable { get; set; }

        /// <summary>
        /// 各任务分析结果
        /// </summary>
        public List<TaskAnalysisResult> TaskResults { get; set; } = new List<TaskAnalysisResult>();

        /// <summary>
        /// 成本函数值
        /// </summary>
        public double CostValue { get; set; }

        /// <summary>
        /// 统计信息
        /// </summary>
        public SchedulabilityStatistics Statistics { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 单个任务的分析结果
    /// </summary>
    public class TaskAnalysisResult
    {
        /// <summary>
        /// 任务名称
        /// </summary>
        public string TaskName { get; set; }

        /// <summary>
        /// 任务索引
        /// </summary>
        public int TaskIndex { get; set; }

        /// <summary>
        /// 优先级
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 周期(微秒)
        /// </summary>
        public int PeriodUs { get; set; }

        /// <summary>
        /// 执行时间(微秒)
        /// </summary>
        public int ExecutionTimeUs { get; set; }

        /// <summary>
        /// 偏移量(微秒)
        /// </summary>
        public int OffsetUs { get; set; }

        /// <summary>
        /// 截止时间(微秒)
        /// </summary>
        public int DeadlineUs { get; set; }

        /// <summary>
        /// 最坏情况响应时间(微秒)
        /// </summary>
        public int WcrtUs { get; set; }

        /// <summary>
        /// 是否满足截止时间
        /// </summary>
        public bool MeetsDeadline { get; set; }

        /// <summary>
        /// 响应时间比率 (WCRT/Deadline)
        /// </summary>
        public double ResponseTimeRatio { get; set; }

        /// <summary>
        /// 延迟时间(微秒) - 超过截止时间的部分
        /// </summary>
        public int LatenessUs { get; set; }

        /// <summary>
        /// 迭代次数
        /// </summary>
        public int IterationCount { get; set; }

        /// <summary>
        /// 是否收敛
        /// </summary>
        public bool Converged { get; set; }

        /// <summary>
        /// 阻塞时间(微秒) - 由低优先级不可抢占任务引起
        /// </summary>
        public int BlockingTimeUs { get; set; }
    }

    /// <summary>
    /// 可调度性统计信息
    /// </summary>
    public class SchedulabilityStatistics
    {
        /// <summary>
        /// 总任务数
        /// </summary>
        public int TotalTasks { get; set; }

        /// <summary>
        /// 可调度任务数
        /// </summary>
        public int SchedulableTasks { get; set; }

        /// <summary>
        /// 错过截止时间的任务数
        /// </summary>
        public int DeadlineMissCount { get; set; }

        /// <summary>
        /// 最大响应时间比率
        /// </summary>
        public double MaxResponseTimeRatio { get; set; }

        /// <summary>
        /// 平均响应时间比率
        /// </summary>
        public double AverageResponseTimeRatio { get; set; }

        /// <summary>
        /// 响应时间比率的方差
        /// </summary>
        public double ResponseTimeRatioVariance { get; set; }

        /// <summary>
        /// 总延迟时间(毫秒)
        /// </summary>
        public double TotalLatenessMs { get; set; }

        /// <summary>
        /// 最大偏移量(微秒)
        /// </summary>
        public int MaxOffsetUs { get; set; }

        /// <summary>
        /// CPU利用率(百分比)
        /// </summary>
        public double CpuUsage { get; set; }
    }

    #endregion
}