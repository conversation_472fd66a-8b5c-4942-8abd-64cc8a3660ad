﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Windows.Forms;
using System.Linq;
using System.Drawing;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace CalculateOffset
{
    public partial class Form1 : Form
    {
        // 应用程序配置
        private AppConfig _appConfig;

        // 数据IO管理器
        private DataIO _dataIO;

        // DataGridView管理器
        private DgvManager _dgvManager;

        // 是否正在加载
        private bool _isLoading = false;
        
        // 当前计算任务引用，用于强制取消
        private Task _currentCalculationTask = null;
        private CancellationTokenSource _masterCancellationTokenSource = null;
        private bool _calculationForceStopRequested = false;

        // 后台任务跟踪列表，用于确保所有任务都能被正确取消
        private readonly List<Task> _backgroundTasks = new List<Task>();
        private readonly object _backgroundTasksLock = new object();

        // 线程泄漏检测
        private int _threadCountBeforeCalculation = 0;

        public Form1()
        {
            InitializeComponent();

            // 初始化应用程序配置
            _appConfig = new AppConfig();

            // 初始化数据IO管理器，使用委托提供最新的配置
            _dataIO = new DataIO(() => _appConfig);

            // 订阅配置导入事件
            _dataIO.ConfigImported += OnConfigImported;

            // 加载上次保存的配置
            LoadSavedConfig();
        }

        /// <summary>
        /// 配置导入事件处理
        /// </summary>
        private void OnConfigImported(AppConfig importedConfig)
        {
            if (importedConfig != null)
            {
                _appConfig = importedConfig;
            }
        }

        /// <summary>
        /// 窗体加载事件
        /// </summary>
        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                // 设置控件初始值
                _isLoading = true;

                // 时基单位固定为ms，不再提供us选项
                if (cboTimeUnit.Items.Count == 0)
                {
                    cboTimeUnit.Items.Add("ms");
                }
                // 固定选择ms，隐藏时基单位选择
                cboTimeUnit.SelectedIndex = 0;
                cboTimeUnit.Enabled = false;

                // 设置时基，将内部微秒值转换为界面毫秒值显示
                double timeBaseMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.TimeBaseUs);
                txtTimeBase.Text = timeBaseMs.ToString("F1");

                // 时基单位固定为ms
                cboTimeUnit.SelectedIndex = 0;

                // 设置最大Offset，将内部微秒值转换为界面毫秒值显示
                double maxOffsetMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.MaxOffsetUs);
                txtMaxOffset.Text = maxOffsetMs.ToString("F0");

                // 设置抖动时间，将内部微秒值转换为界面毫秒值显示
                double jitterTimeMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.JitterTimeUs);
                txtJitterTime.Text = jitterTimeMs.ToString("F3");

                // 设置计算类型
                cboCalculationType.SelectedIndex = (int)_appConfig.MainConfig.CalculationType;

                _isLoading = false;

                // 确保初始时基值符合要求
                ValidateTimeBase();

                // 初始化DataGridView管理器
                _dgvManager = new DgvManager(dgvTasks, _appConfig, _dataIO);

                // 设置状态栏
                UpdateStatusBar("就绪");

                // 注册窗体关闭事件，确保保存最后的配置
                this.FormClosing += Form1_FormClosing;

                // 注册时基输入框失去焦点事件
                txtTimeBase.LostFocus += TxtTimeBase_LostFocus;

                // 注册时基单位变更事件
                cboTimeUnit.SelectedIndexChanged += CboTimeUnit_SelectedIndexChanged;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"窗体加载错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 时基输入框失去焦点事件
        /// </summary>
        private void TxtTimeBase_LostFocus(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                // 验证时基值
                ValidateTimeBase();

                // 更新配置
                UpdateConfigFromUI();

                // 触发自动保存
                _dataIO.AutoSaveConfig();
            }
            catch (Exception ex)
            {
                // 静默处理时基值验证错误
            }
        }

        /// <summary>
        /// 时基单位变更事件
        /// </summary>
        private void CboTimeUnit_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                // 验证时基值
                ValidateTimeBase();

                // 更新配置
                UpdateConfigFromUI();

                // 触发自动保存
                _dataIO.AutoSaveConfig();
            }
            catch (Exception ex)
            {
                // 静默处理时基单位变更错误
            }
        }

        /// <summary>
        /// 配置项变更事件
        /// </summary>
        private void ConfigChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                // 如果是时基单位变更或时基值变更，验证会在专门的事件处理程序中进行
                // 不在此处验证时基值，避免输入过程中过早触发校验

                // 更新配置项
                UpdateConfigFromUI();

                // 触发自动保存
                _dataIO.AutoSaveConfig();
            }
            catch (Exception ex)
            {
                // 静默处理配置项变更错误
            }
        }

        /// <summary>
        /// 窗体关闭事件
        /// </summary>
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                // 强制停止所有计算任务
                ForceStopCurrentCalculation();

                // 确保在关闭前保存最新的配置
                UpdateConfigFromUI();
                _dataIO.SaveConfigImmediately();

                // 清理资源
                _masterCancellationTokenSource?.Dispose();

                // 检查是否还有未完成的后台任务
                lock (_backgroundTasksLock)
                {
                    if (_backgroundTasks.Count > 0)
                    {
                        Console.WriteLine($"⚠️ 应用程序关闭时仍有 {_backgroundTasks.Count} 个后台任务未完成");
                    }
                }
            }
            catch (Exception ex)
            {
                // 静默处理窗体关闭保存错误
                Console.WriteLine($"⚠️ 窗体关闭时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载保存的配置
        /// </summary>
        private void LoadSavedConfig()
        {
            try
            {
                AppConfig savedConfig = _dataIO.LoadAutoSavedConfig();
                // 由于现在通过事件处理，不需要在这里更新_appConfig
            }
            catch (Exception ex)
            {
                // 静默处理加载保存配置错误
            }
        }

        /// <summary>
        /// 验证时基值，确保转换为微秒后是100us的整数倍
        /// </summary>
        private void ValidateTimeBase()
        {
            if (double.TryParse(txtTimeBase.Text, out double timeBaseMs))
            {
                if (timeBaseMs <= 0)
                {
                    // 显示提示消息
                    MessageBox.Show($"时基值必须大于0。\n已自动调整为: 1",
                        "时基值调整", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 更新文本框
                    txtTimeBase.Text = "1";
                    return;
                }

                // 转换为微秒进行校验
                int timeBaseUs = TaskUtility.ConvertToMicroseconds(timeBaseMs);
                
                // 检查是否是100us的整数倍
                if (timeBaseUs % TaskUtility.MIN_TIME_BASE_US != 0)
                {
                    // 调整为最接近的100us的整数倍
                    int adjustedTimeBaseUs = ((timeBaseUs + TaskUtility.MIN_TIME_BASE_US / 2) / TaskUtility.MIN_TIME_BASE_US) * TaskUtility.MIN_TIME_BASE_US;
                    
                    // 确保至少是100us
                    adjustedTimeBaseUs = Math.Max(TaskUtility.MIN_TIME_BASE_US, adjustedTimeBaseUs);
                    
                    // 转换回毫秒显示
                    double adjustedTimeBaseMs = TaskUtility.ConvertToMilliseconds((int)adjustedTimeBaseUs);
                    
                    // 显示提示消息
                    MessageBox.Show($"时基值转换为微秒后必须是{TaskUtility.MIN_TIME_BASE_US}us的整数倍。\n" +
                                  $"原值: {timeBaseMs:F1}ms ({timeBaseUs}us)\n" +
                                  $"已自动调整为: {adjustedTimeBaseMs:F1}ms ({adjustedTimeBaseUs}us)",
                        "时基值调整", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // 更新文本框，显示1位小数精度
                    txtTimeBase.Text = adjustedTimeBaseMs.ToString("F1");
                }
            }
        }

        /// <summary>
        /// 从UI更新配置
        /// </summary>
        private void UpdateConfigFromUI()
        {
            try
            {
                // 更新时基，将界面毫秒值转换为内部微秒值存储
                if (double.TryParse(txtTimeBase.Text, out double timeBaseMs))
                {
                    // 转换为微秒值，确保最小值为100us
                    int timeBaseUs = Math.Max(TaskUtility.MIN_TIME_BASE_US, TaskUtility.ConvertToMicroseconds(timeBaseMs));
                    _appConfig.MainConfig.TimeBaseUs = timeBaseUs;
                }

                // 时基单位固定为ms（Models.cs中已删除TimeUnit属性）

                // 更新最大Offset，将界面毫秒值转换为内部微秒值存储（只支持整数）
                if (int.TryParse(txtMaxOffset.Text, out int maxOffsetMs))
                {
                    // 转换为微秒值，确保最小值为1000us - 1ms
                    int maxOffsetUs = Math.Max(1000, TaskUtility.ConvertToMicroseconds(maxOffsetMs));
                    _appConfig.MainConfig.MaxOffsetUs = maxOffsetUs;
                }

                // 更新抖动时间，将界面毫秒值转换为内部微秒值存储
                if (double.TryParse(txtJitterTime.Text, out double jitterTimeMs))
                {
                    // 转换为微秒值，确保最小值为0us，最大值为1000us(1ms)
                    int jitterTimeUs = Math.Max(0, Math.Min(1000, TaskUtility.ConvertToMicroseconds(jitterTimeMs)));
                    _appConfig.MainConfig.JitterTimeUs = jitterTimeUs;
                    
                    // 更新所有任务的抖动时间
                    if (_dgvManager != null)
                    {
                        var tasks = _dgvManager.GetAllTasks();
                        foreach (var task in tasks)
                        {
                            task.JitterTimeUs = jitterTimeUs;
                        }
                    }
                }

                // 更新计算类型
                if (cboCalculationType.SelectedIndex >= 0)
                {
                    _appConfig.MainConfig.CalculationType = (CalculationType)cboCalculationType.SelectedIndex;
                }

                // 确保任务列表已更新到配置对象
                if (_dgvManager != null)
                {
                    _appConfig.Tasks = _dgvManager.GetAllTasks();
                }
            }
            catch (Exception ex)
            {
                // 静默处理更新UI配置错误
            }
        }

        /// <summary>
        /// 重置按钮点击事件
        /// </summary>
        private void btnReset_Click(object sender, EventArgs e)
        {
            try
            {
                // 确认重置
                DialogResult result = MessageBox.Show("确定要重置所有配置吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    return;
                }

                // 重置配置项
                _isLoading = true;

                // 重置主界面配置
                _appConfig.MainConfig = new MainConfig();

                // 更新UI，将内部微秒值转换为界面毫秒值显示
                double timeBaseMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.TimeBaseUs);
                txtTimeBase.Text = timeBaseMs.ToString("F1");

                // 时基单位固定为ms
                cboTimeUnit.SelectedIndex = 0;

                // 将内部微秒值转换为界面毫秒值显示
                double maxOffsetMsReset = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.MaxOffsetUs);
                txtMaxOffset.Text = maxOffsetMsReset.ToString("F0");

                // 重置计算类型
                cboCalculationType.SelectedIndex = (int)_appConfig.MainConfig.CalculationType;

                // 重置任务列表
                _appConfig.Tasks.Clear();
                if (_dgvManager != null)
                {
                    _dgvManager.ClearAllTasks();
                }

                _isLoading = false;

                // 触发自动保存
                _dataIO.AutoSaveConfig();

                // 更新状态栏
                UpdateStatusBar("已重置所有配置");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 全部清除按钮点击事件
        /// </summary>
        private void btnClearAll_Click(object sender, EventArgs e)
        {
            try
            {
                // 确认清除
                DialogResult result = MessageBox.Show("确定要清除所有配置吗？", "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result != DialogResult.Yes)
                {
                    return;
                }

                // 清除配置项
                _isLoading = true;

                // 清除主界面配置值
                txtTimeBase.Text = "";
                cboTimeUnit.SelectedIndex = -1;
                txtMaxOffset.Text = "";

                // 清除任务列表
                if (_dgvManager != null)
                {
                    _dgvManager.ClearAllTasks();
                }

                _isLoading = false;

                // 更新配置并保存
                UpdateConfigFromUI();
                _dataIO.AutoSaveConfig();

                // 更新状态栏
                UpdateStatusBar("已清除所有配置");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导入按钮点击事件
        /// </summary>
        private void btnImport_Click(object sender, EventArgs e)
        {
            try
            {
                // 设置初始目录
                if (!string.IsNullOrEmpty(_appConfig.MainConfig.LastOpenPath))
                {
                    string directory = Path.GetDirectoryName(_appConfig.MainConfig.LastOpenPath);
                    if (Directory.Exists(directory))
                    {
                        openFileDialog.InitialDirectory = directory;
                    }
                }
                else
                {
                    openFileDialog.InitialDirectory = Path.GetDirectoryName(Application.ExecutablePath);
                }

                // 显示导入对话框
                if (openFileDialog.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                // 导入配置
                AppConfig importedConfig = _dataIO.ImportConfig(openFileDialog.FileName);
                if (importedConfig == null)
                {
                    return;
                }

                // 更新UI - 配置已经通过事件处理程序更新
                _isLoading = true;

                // 将内部微秒值转换为界面毫秒值显示
                double timeBaseMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.TimeBaseUs);
                txtTimeBase.Text = timeBaseMs.ToString("F1");

                // 时基单位固定为ms
                cboTimeUnit.SelectedIndex = 0;

                // 将内部微秒值转换为界面毫秒值显示
                double maxOffsetMsImport = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.MaxOffsetUs);
                txtMaxOffset.Text = maxOffsetMsImport.ToString("F0");

                // 更新任务列表
                if (_dgvManager != null)
                {
                    _dgvManager.UpdateTasks(_appConfig.Tasks);
                }

                _isLoading = false;

                // 确保保存最新配置
                _dataIO.AutoSaveConfig();

                // 更新状态栏
                UpdateStatusBar($"已导入配置：{openFileDialog.FileName}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                // 设置初始目录
                if (!string.IsNullOrEmpty(_appConfig.MainConfig.LastSavePath))
                {
                    string directory = Path.GetDirectoryName(_appConfig.MainConfig.LastSavePath);
                    if (Directory.Exists(directory))
                    {
                        saveFileDialog.InitialDirectory = directory;
                    }
                }
                else
                {
                    saveFileDialog.InitialDirectory = Path.GetDirectoryName(Application.ExecutablePath);
                }

                // 显示导出对话框
                if (saveFileDialog.ShowDialog() != DialogResult.OK)
                {
                    return;
                }

                // 更新配置
                UpdateConfigFromUI();

                // 导出配置
                if (_dataIO.ExportConfig(saveFileDialog.FileName))
                {
                    // 更新状态栏
                    UpdateStatusBar($"已导出配置：{saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导出操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 计算按钮点击事件（带进度条窗口版本）
        /// </summary>
        private void btnCalculate_Click(object sender, EventArgs e)
        {
            try
            {
                // 更新配置
                UpdateConfigFromUI();
                
                // 清除之前的会话数据
                TaskUtility.ClearSessionLcm();

                // 获取任务列表
                if (_dgvManager == null)
                {
                    UpdateStatusBar("数据网格未初始化");
                    return;
                }

                List<TaskItem> allTasks = _dgvManager.GetAllTasks();

                // 严格过滤有效任务
                List<TaskItem> validTasks = FilterValidTasks(allTasks);

                // 检查是否有有效任务
                if (validTasks.Count == 0)
                {
                    MessageBox.Show("没有有效的任务可计算！\n\n请确保至少有一个任务满足以下条件：\n" +
                                  "• 任务名称不为空\n" +
                                  "• 周期大于0\n",
                        "无有效任务", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    UpdateStatusBar("没有有效的任务可计算");
                    return;
                }

                // 显示过滤结果
                if (validTasks.Count < allTasks.Count)
                {
                    int filteredCount = allTasks.Count - validTasks.Count;
                    UpdateStatusBar($"已过滤掉 {filteredCount} 个无效任务，准备计算...");
                }

                // 快速预检查：仅基于执行时间与每任务1次jitter的必要条件判断，避免无效的重型计算
                string quickMsg;
                if (!TaskValidator.QuickFeasibilityCheck(validTasks, out quickMsg))
                {
                    MessageBox.Show(quickMsg, "不可调度(快速检查)", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    UpdateStatusBar("快速可调度性预检查失败，已跳过优化计算");
                    return;
                }

                // 验证前置任务关系
                var validationResult = TaskValidator.ValidatePrerequisiteTasks(validTasks);
                if (!validationResult.IsValid)
                {
                    MessageBox.Show($"前置任务验证失败：\n\n{validationResult.ErrorMessage}",
                        "验证错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    UpdateStatusBar("前置任务验证失败");
                    return;
                }

                // 获取时基值(内部已存储为微秒)
                int timeBaseUs = _appConfig.MainConfig.TimeBaseUs;

                // 获取用户设定的最大offset值(内部已存储为微秒)
                int globalMaxOffsetUs = _appConfig.MainConfig.MaxOffsetUs;

                // 统一更新所有任务的最大offset限制（取任务周期和用户设定的较小值）
                UpdateTaskMaxOffsetLimits(validTasks, globalMaxOffsetUs);

                // 设置当前会话的LCM值（一次性计算）
                TaskUtility.SetSessionLcm(validTasks);

                // 根据计算类型选择不同的计算方式
                CalculationType calculationType = _appConfig.MainConfig.CalculationType;
                
                // 禁用计算按钮，防止重复点击
                btnCalculate.Enabled = false;

                // 记录计算开始前的线程数
                _threadCountBeforeCalculation = ThreadLeakTest.GetCurrentThreadCount();
                Console.WriteLine($"🧵 计算开始前线程数: {_threadCountBeforeCalculation}");
                ThreadLeakTest.PrintThreadInfo();

                // 重置强制停止标志和创建新的取消令牌源
                _calculationForceStopRequested = false;
                _masterCancellationTokenSource = new CancellationTokenSource();
                
                // 创建并显示进度条窗口，传递主窗体引用
                ProgressForm progressForm;
                if (calculationType == CalculationType.Message)
                {
                    progressForm = new ProgressForm("报文模式计算进度", this);
                    // 在后台线程中运行报文模式计算
                    StartCalculationWithProgress(progressForm, () => CalculateMessageOffsetsAsync(validTasks, timeBaseUs, progressForm));
                }
                else
                {
                    progressForm = new ProgressForm("任务模式计算进度", this);
                    // 在后台线程中运行任务模式计算
                    StartCalculationWithProgress(progressForm, () => CalculateTaskOffsetsAsync(validTasks, timeBaseUs, progressForm));
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"计算操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                btnCalculate.Enabled = true; // 发生异常时重新启用按钮
            }
        }

        /// <summary>
        /// 启动带进度条的计算
        /// </summary>
        private async void StartCalculationWithProgress(ProgressForm progressForm, Func<Task> calculationTask)
        {
            // 显示进度条窗口（非阻塞模式）
            progressForm.Show(this);
            
            // 开始总体计算时间统计
            PerformanceTimer.Start("总体计算时间");
            
            try
            {
                // 更新初始状态
                progressForm.UpdateProgress(0, "正在初始化计算...", $"检测到CPU核心数: {Environment.ProcessorCount}");
                
                // 保存当前计算任务引用，并执行实际计算
                _currentCalculationTask = calculationTask();
                
                // 定期检查强制停止标志
                var forceStopCheckTask = Task.Run(async () =>
                {
                    while (!_currentCalculationTask.IsCompleted && !_calculationForceStopRequested)
                    {
                        await Task.Delay(100); // 每100ms检查一次
                    }

                    if (_calculationForceStopRequested && !_currentCalculationTask.IsCompleted)
                    {
                        Console.WriteLine("🚫 检测到强制停止请求，中断计算等待");
                        _masterCancellationTokenSource?.Cancel();
                        throw new OperationCanceledException("用户强制取消计算");
                    }
                });

                // 将强制停止检查任务添加到跟踪列表
                AddBackgroundTask(forceStopCheckTask);

                // 等待计算完成或强制停止
                await Task.WhenAny(_currentCalculationTask, forceStopCheckTask);
                
                // 如果是强制停止，抛出取消异常
                if (_calculationForceStopRequested)
                {
                    throw new OperationCanceledException("用户强制取消计算");
                }
                
                // 等待计算任务真正完成
                await _currentCalculationTask;
                
                // 计算完成，短暂显示完成状态
                progressForm.UpdateProgress(100, "计算完成！", "所有优化计算已成功完成");
                await Task.Delay(500); // 缩短显示时间
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine("⚠️ 用户取消了计算操作");
                progressForm.UpdateProgress(0, "已取消", "计算操作已被用户取消");
                UpdateStatusBar("计算已被用户取消");
                await Task.Delay(500);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 计算过程中发生错误: {ex.Message}");
                progressForm.UpdateProgress(0, "计算失败", $"错误: {ex.Message}");
                MessageBox.Show($"计算过程中发生错误: {ex.Message}", "计算错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                UpdateStatusBar("计算失败");
                await Task.Delay(500);
            }
            finally
            {
                // 结束总体计算时间统计
                PerformanceTimer.Stop("总体计算时间");
                
                // 输出性能统计摘要
                PerformanceTimer.PrintSummary();
                
                // **关键修改：先关闭进度条，再显示报告**
                progressForm.SafeClose();
                
                // 重新启用计算按钮
                btnCalculate.Enabled = true;

                // 检测线程泄漏
                Task.Run(async () =>
                {
                    await Task.Delay(1000); // 等待1秒让所有任务完成
                    int threadCountAfterCalculation = ThreadLeakTest.GetCurrentThreadCount();
                    Console.WriteLine($"🧵 计算结束后线程数: {threadCountAfterCalculation}");

                    bool hasLeak = await ThreadLeakTest.DetectThreadLeak(_threadCountBeforeCalculation, threadCountAfterCalculation);
                    if (hasLeak)
                    {
                        Console.WriteLine("🚨 警告：检测到可能的线程泄漏！");
                        ThreadLeakTest.PrintThreadInfo();
                    }
                });
                

            }
        }





        /// <summary>
        /// 添加后台任务到跟踪列表
        /// </summary>
        private void AddBackgroundTask(Task task)
        {
            if (task == null) return;

            lock (_backgroundTasksLock)
            {
                _backgroundTasks.Add(task);
                Console.WriteLine($"📝 添加后台任务到跟踪列表，当前任务数: {_backgroundTasks.Count}");
            }

            // 任务完成后自动从列表中移除
            task.ContinueWith(t =>
            {
                lock (_backgroundTasksLock)
                {
                    _backgroundTasks.Remove(t);
                    Console.WriteLine($"🗑️ 后台任务已完成并移除，剩余任务数: {_backgroundTasks.Count}");
                }
            }, TaskContinuationOptions.ExecuteSynchronously);
        }

        /// <summary>
        /// 强制停止所有后台任务
        /// </summary>
        private async Task ForceStopAllBackgroundTasks()
        {
            List<Task> tasksToStop;
            lock (_backgroundTasksLock)
            {
                tasksToStop = new List<Task>(_backgroundTasks);
                Console.WriteLine($"🚫 准备强制停止 {tasksToStop.Count} 个后台任务");
            }

            if (tasksToStop.Count == 0) return;

            try
            {
                // 等待所有任务完成，最多等待3秒
                var timeoutTask = Task.Delay(3000);
                var allTasksTask = Task.WhenAll(tasksToStop.Where(t => !t.IsCompleted));

                var completedTask = await Task.WhenAny(allTasksTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    Console.WriteLine("⚠️ 警告：部分后台任务在3秒内未完成，但已恢复UI状态");

                    // 清理跟踪列表中已完成的任务
                    lock (_backgroundTasksLock)
                    {
                        _backgroundTasks.RemoveAll(t => t.IsCompleted);
                        if (_backgroundTasks.Count > 0)
                        {
                            Console.WriteLine($"⚠️ 仍有 {_backgroundTasks.Count} 个任务未完成");
                        }
                    }
                }
                else
                {
                    Console.WriteLine("✅ 所有后台任务已成功停止");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 停止后台任务时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 恢复计算按钮可用状态（线程安全）
        /// </summary>
        public void RestoreCalculateButton()
        {
            if (this.InvokeRequired)
            {
                this.Invoke(new Action(RestoreCalculateButton));
                return;
            }

            btnCalculate.Enabled = true;
            UpdateStatusBar("计算已取消，可以重新开始计算");
            Console.WriteLine("✅ 计算按钮已恢复可用状态");
        }

        /// <summary>
        /// 强制停止当前计算任务
        /// </summary>
        public void ForceStopCurrentCalculation()
        {
            try
            {
                _calculationForceStopRequested = true;

                // 取消主CancellationTokenSource
                if (_masterCancellationTokenSource != null && !_masterCancellationTokenSource.IsCancellationRequested)
                {
                    _masterCancellationTokenSource.Cancel();
                    Console.WriteLine("🚫 主取消令牌已触发");
                }

                // 强制停止当前计算任务
                if (_currentCalculationTask != null && !_currentCalculationTask.IsCompleted)
                {
                    Console.WriteLine("🚫 尝试强制停止计算任务...");
                }

                // 强制停止所有后台任务
                var stopTasksTask = ForceStopAllBackgroundTasks();
                AddBackgroundTask(stopTasksTask);

                // 检测强制停止后的线程状态
                var threadCheckTask = Task.Run(async () =>
                {
                    await Task.Delay(2000); // 等待2秒让停止操作完成
                    int currentThreadCount = ThreadLeakTest.GetCurrentThreadCount();
                    Console.WriteLine($"🧵 强制停止后线程数: {currentThreadCount}");
                    ThreadLeakTest.MonitorThreadCount("强制停止", _threadCountBeforeCalculation);
                });
                AddBackgroundTask(threadCheckTask);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 强制停止计算时发生异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算任务模式的Offset（异步版本，支持进度回调）
        /// </summary>
        private async Task CalculateTaskOffsetsAsync(List<TaskItem> validTasks, int timeBaseUs, ProgressForm progressForm)
        {
            PerformanceTimer.Start("任务模式计算");
            
            try
            {
                this.Invoke((Action)(() => UpdateStatusBar("正在使用智能调度引擎计算最优Offset...")));
                progressForm.UpdateProgress(5, "智能调度引擎启动", "正在初始化双轨优化算法...");

                // 在当前线程中运行智能调度（避免线程嵌套问题）
                IntelligentSchedulingResult schedulingResult = null;
                
                // 创建智能调度引擎
                var engine = new IntelligentSchedulingEngine(timeBaseUs);
                var parameters = new IntelligentSchedulingParameters
                {
                    UseIntelligentSA = true,
                    UseImprovedGA = true,
                    UseEarlyStoppingForPerfectSolution = false, // 关闭早停，完整搜索所有offset
                    UseEarlyStopping = false,                   // 关闭早停，完整搜索所有offset
                    SAMaxIterations = 20000,//40000,  // 再次增加一倍：从20000增加到40000 (充分利用算力)
                    GAMaxGenerations = 300,//600,   // 再次增加一倍：从300增加到600 (充分利用算力)
                    GAPopulationSize = 150,   // 增加种群大小：从100增加到150 (50%增长)
                    SAMaxRestarts = 5,        // 增加重启次数：从3增加到5 (66%增长)
                    UseMultiStartStrategies = true,
                    UseBasicStrategies = true,
                    UseExtendedStrategies = true,
                };

                // 创建智能进度回调（支持轮次自动进度）
                int lastRoundNumber = -1; // 记录上一个轮次号，避免重复启动
                Action<int, string, string> progressCallback = (percentage, status, detail) =>
                {
                    int adjustedPercentage = Math.Min(95, 10 + (percentage * 85 / 100)); // 将0-100%映射到10-95%
                    
                    // 检测轮次开始，格式："第 X/Y 轮优化"
                    if (!string.IsNullOrEmpty(status))
                    {
                        Console.WriteLine($"🔍 检查状态信息：'{status}'");
                        
                        if (status.Contains("轮优化"))
                        {
                            try
                            {
                                // 解析轮次信息
                                var match = System.Text.RegularExpressions.Regex.Match(status, @"第 (\d+)/(\d+) 轮优化");
                                Console.WriteLine($"🔍 正则匹配结果：Success={match.Success}");
                                
                                if (match.Success)
                                {
                                    int currentRound = int.Parse(match.Groups[1].Value);
                                    int totalRounds = int.Parse(match.Groups[2].Value);
                                    
                                    Console.WriteLine($"🔍 解析轮次：当前={currentRound}, 总计={totalRounds}, 上次={lastRoundNumber}");
                                    
                                    // 避免重复启动同一轮次的自动进度
                                    if (currentRound != lastRoundNumber)
                                    {
                                        lastRoundNumber = currentRound;
                                        
                                        // 计算该轮次的进度范围：智能分配进度区间
                                        int roundStartProgress = adjustedPercentage; // 使用当前的调整后进度作为起点
                                        int nextRoundProgress = Math.Min(93, 10 + ((currentRound * 85) / totalRounds)); // 下一轮的理论起点
                                        int roundEndProgress = Math.Max(roundStartProgress + 8, nextRoundProgress - 2); // 确保每轮至少8%进度，为下轮留2%余量
                                        
                                        Console.WriteLine($"🔍 计算进度范围：起始={roundStartProgress}%, 结束={roundEndProgress}%");
                                        
                                        // 确保进度范围合理
                                        if (roundEndProgress > roundStartProgress + 3) // 至少3%的增长空间
                                        {
                                            string roundName = $"第{currentRound}/{totalRounds}轮优化";
                                            Console.WriteLine($"🚀 启动轮次自动进度：{roundName}");
                                            progressForm.StartRoundAutoProgress(roundStartProgress, roundEndProgress, roundName);
                                        }
                                        else
                                        {
                                            Console.WriteLine($"⚠️ 进度范围不足，跳过自动进度启动");
                                        }
                                    }
                                    else
                                    {
                                        Console.WriteLine($"⚠️ 重复轮次，跳过");
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"⚠️ 正则匹配失败，状态：'{status}'");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"❌ 解析轮次进度信息失败: {ex.Message}");
                            }
                        }
                    }
                    
                    // 检测计算完成信号，停止自动进度
                    if (!string.IsNullOrEmpty(status) && (status.Contains("计算完成") || adjustedPercentage >= 90))
                    {
                        progressForm.StopAutoProgress();
                    }
                    
                    progressForm.UpdateProgress(adjustedPercentage, status, detail);
                };

                // 启动基础自动进度作为备用方案（防止轮次检测失败）
                Console.WriteLine("🔄 启动基础自动进度（备用方案）");
                progressForm.StartAutoProgress(10, 80); // 10%-80%的基础增长
                
                // 检查强制停止标志
                if (_calculationForceStopRequested)
                {
                    throw new OperationCanceledException("用户强制取消计算");
                }

                // 运行智能调度（传入进度回调），使用组合的取消令牌
                var combinedTokenSource = CancellationTokenSource.CreateLinkedTokenSource(
                    progressForm.CancellationToken, 
                    _masterCancellationTokenSource?.Token ?? CancellationToken.None);
                
                schedulingResult = await Task.Run(() => 
                    engine.RunIntelligentScheduling(validTasks, _appConfig.MainConfig.MaxOffsetUs, parameters, progressCallback, combinedTokenSource.Token));

                if (progressForm.CancellationRequested)
                {
                    Console.WriteLine("⚠️ 用户取消了任务模式计算");
                    return;
                }

                // 处理调度结果
                if (schedulingResult != null && schedulingResult.Success)
                {
                    progressForm.UpdateProgress(96, "应用计算结果", "正在更新任务Offset值...");

                    // 输出详细报告到控制台
                    Console.WriteLine("\n" + new string('=', 60));
                    Console.WriteLine("🎯 智能调度引擎计算完成");
                    Console.WriteLine(new string('=', 60));

                    //if (schedulingResult.SchedulabilityResult != null)
                    //{
                    //    var result = schedulingResult.SchedulabilityResult;
                    //    Console.WriteLine($"✅ 可调度性: {(result.IsSchedulable ? "是" : "否")}");
                    //    if (result.Statistics != null)
                    //    {
                    //        Console.WriteLine($"✅ 最大响应时间比率: {result.Statistics.MaxResponseTimeRatio:F3}");
                    //    }
                    //}

                    //Console.WriteLine(new string('=', 60) + "\n");

                    // 停止自动进度增长，设置最终进度
                    progressForm.StopAutoProgress();
                    progressForm.UpdateProgress(98, "结果应用完成", "计算完成...");

                    // 保存结果，进度条关闭后再调用原有的ProcessIntelligentSchedulingResult
                    var finalSchedulingResult = schedulingResult;
                    var finalValidTasks = validTasks;
                    var finalTimeBaseUs = timeBaseUs;
                    
                    // 设置在进度条关闭后执行的任务
                    Task.Run(async () =>
                    {
                        // 等待进度条关闭
                        await Task.Delay(200);
                        
                        // 在UI线程中调用原有的ProcessIntelligentSchedulingResult
                        this.Invoke((Action)(() =>
                        {
                            ProcessIntelligentSchedulingResult(finalSchedulingResult, finalValidTasks, finalTimeBaseUs);
                        }));
                    });
                }
                else
                {
                    this.Invoke((Action)(() =>
                    {
                        string errorMsg = schedulingResult?.ErrorMessage ?? "未知错误";
                        MessageBox.Show($"智能调度计算失败: {errorMsg}", "计算失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        UpdateStatusBar("任务模式计算失败");
                    }));
                }
            }
            finally
            {
                PerformanceTimer.Stop("任务模式计算");
            }
        }

        /// <summary>
        /// 计算报文模式的Offset（异步版本，支持进度回调）
        /// </summary>
        private async Task CalculateMessageOffsetsAsync(List<TaskItem> validTasks, int timeBaseUs, ProgressForm progressForm)
        {
            PerformanceTimer.Start("报文模式计算");
            
            try
            {
                this.Invoke((Action)(() => UpdateStatusBar("正在使用OffsetCalculator计算报文最优Offset...")));
                progressForm.UpdateProgress(10, "OffsetCalculator启动", "正在初始化最优搜索算法...");

                // OffsetCalculator通常执行较快，使用简单的自动进度（可选）
                progressForm.StartAutoProgress(15, 80); // 15%-80%的范围

                // 检查强制停止标志
                if (_calculationForceStopRequested)
                {
                    throw new OperationCanceledException("用户强制取消计算");
                }

                await Task.Run(() =>
                {
                    // 调用OffsetCalculatorBased策略，开启二分查找模式
                    progressForm.UpdateProgress(20, "开始二分查找优化", "正在计算最优Offset配置...");
                    
                    // 创建任务副本以避免在后台线程中修改与UI绑定的原始任务对象
                    List<TaskItem> clonedTasks = TaskUtility.CloneTasks(validTasks);
                    
                    var offsetCalculator = new OffsetCalculator(timeBaseUs, _appConfig.MainConfig.MaxOffsetUs);
                    bool success = offsetCalculator.CalculateOptimalOffsets(clonedTasks, enableMinOffsetSearch: true);

                    if (progressForm.CancellationRequested)
                    {
                        Console.WriteLine("⚠️ 用户取消了报文模式计算");
                        return;
                    }

                    progressForm.UpdateProgress(80, "计算完成", "正在应用结果...");

                    if (success)
                    {
                        // 将克隆任务的结果应用回原始任务列表
                        this.Invoke((Action)(() =>
                        {
                            // 应用克隆任务的Offset值到原始任务
                            foreach (var originalTask in validTasks)
                            {
                                var clonedTask = clonedTasks.FirstOrDefault(t => t.TaskIndex == originalTask.TaskIndex);
                                if (clonedTask != null && clonedTask.OffsetUs.HasValue)
                                {
                                    originalTask.OffsetUs = clonedTask.OffsetUs.Value;
                                }
                            }
                            
                            _dgvManager.RefreshDataGridView();
                            UpdateStatusBar($"报文模式计算完成 - 方差: {offsetCalculator.CalcVariance:F4}");
                            
                            // 计算完成后保存配置
                            UpdateConfigFromUI();
                            _dataIO.AutoSaveConfig();
                        }));

                        // 停止自动进度增长，设置最终进度
                        progressForm.StopAutoProgress();
                        progressForm.UpdateProgress(90, "结果应用完成", "正在生成报告...");

                        // 输出结果到控制台
                        Console.WriteLine("\n" + new string('=', 50));
                        Console.WriteLine("📊 OffsetCalculator报文模式计算完成");
                        Console.WriteLine(new string('=', 50));
                        Console.WriteLine($"✅ 计算成功: 是");
                        Console.WriteLine($"✅ 最终方差: {offsetCalculator.CalcVariance:F6}");
                        Console.WriteLine($"✅ 最佳方差: {offsetCalculator.BestVariance:F6}");
                        Console.WriteLine($"✅ 计算方法: OffsetCalculatorBased + 二分查找");
                        Console.WriteLine(new string('=', 50) + "\n");
                        
                        // 保存结果，进度条关闭后再生成校验报告（和任务模式保持一致）
                        var finalValidTasks = validTasks;
                        var finalTimeBaseUs = timeBaseUs;
                        
                        // 设置在进度条关闭后执行校验和时间轴生成
                        Task.Run(async () =>
                        {
                            // 等待进度条关闭
                            await Task.Delay(200);
                            
                            // 在UI线程中执行校验并显示校验报告
                            this.Invoke((Action)(() =>
                            {
                                // 执行校验并显示校验报告
                                var offsetValidationResult = ExecuteValidationAndShowReport(finalValidTasks, finalTimeBaseUs);
                                
                                // 生成时间轴网页（仅在可调度时生成，避免错误图表）
                                if (offsetValidationResult?.IsSchedulable == true)
                                {
                                    try
                                    {
                                        // 生成时间轴网页
                                        bool htmlSuccess = TimelineGenerator.GenerateTimelineHtml(finalValidTasks, finalTimeBaseUs, "ms", offsetValidationResult);
                                        
                                        if (htmlSuccess)
                                        {
                                            MessageBox.Show("报文模式计算完成！偏移量校验报告已生成。\n时间轴网页已生成，请在浏览器中查看。",
                                                "计算完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"生成时间轴网页失败: {ex.Message}");
                                        MessageBox.Show("报文模式计算完成！偏移量校验报告已生成。\n网页生成失败，请查看日志输出。",
                                            "计算完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    }
                                }
                            }));
                        });
                    }
                    else
                    {
                        this.Invoke((Action)(() =>
                        {
                            MessageBox.Show("OffsetCalculator计算失败", "计算失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            UpdateStatusBar("报文模式计算失败");
                        }));
                    }
                });
            }
            finally
            {
                PerformanceTimer.Stop("报文模式计算");
            }
        }

        /// <summary>
        /// 计算任务模式的Offset（原有同步逻辑，保留用于兼容性）
        /// </summary>
        private void CalculateTaskOffsets(List<TaskItem> validTasks, int timeBaseUs)
        {
            PerformanceTimer.Start("任务模式计算");
            
            try
            {
                UpdateStatusBar("正在使用智能调度引擎计算最优Offset...");

                // 禁用计算按钮，防止重复点击
                btnCalculate.Enabled = false;

                // 在后台线程中运行智能调度
                Task.Run(() =>
                {
                    IntelligentSchedulingResult schedulingResult = null;
                    try
                    {
                        // 创建智能调度引擎
                        var engine = new IntelligentSchedulingEngine(timeBaseUs);
                        var parameters = new IntelligentSchedulingParameters
                        {
                            UseIntelligentSA = true,
                            UseImprovedGA = true,
                            UseEarlyStoppingForPerfectSolution = false, // 关闭早停，完整搜索所有offset
                            UseEarlyStopping = false,                   // 关闭早停，完整搜索所有offset
                            SAMaxIterations = 20000,//40000,  // 再次增加一倍：从20000增加到40000 (充分利用算力)
                            GAMaxGenerations = 300,//600,   // 再次增加一倍：从300增加到600 (充分利用算力)
                            GAPopulationSize = 150,   // 增加种群大小：从100增加到150 (50%增长)
                            SAMaxRestarts = 5,        // 增加重启次数：从3增加到5 (66%增长)
                            // 新增多起点策略参数
                            UseMultiStartStrategies = true,
                            UseBasicStrategies = true,
                            UseExtendedStrategies = true,
                        };

                        // 执行智能调度 - 使用过滤后的有效任务  
                        int maxOffsetUs = _appConfig.MainConfig.MaxOffsetUs;
                        schedulingResult = engine.RunIntelligentScheduling(validTasks, maxOffsetUs, parameters);
                    }
                    catch (Exception ex)
                    {
                        // 在UI线程中显示错误
                        this.Invoke((Action)(() =>
                        {
                            MessageBox.Show($"智能调度执行出错: {ex.Message}", "错误",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                            UpdateStatusBar("智能调度失败");
                            btnCalculate.Enabled = true;
                        }));
                        return;
                    }

                    // 在UI线程中处理结果
                    this.Invoke((Action)(() =>
                    {
                        try
                        {
                            // 将优化结果应用回原始任务列表
                            ProcessIntelligentSchedulingResult(schedulingResult, validTasks, timeBaseUs);
                            
                            // 计算完成后保存配置
                            UpdateConfigFromUI();
                            _dataIO.AutoSaveConfig();
                        }
                        finally
                        {
                            btnCalculate.Enabled = true;
                        }
                    }));
                });
            }
            catch (Exception ex)
            {
                MessageBox.Show($"任务计算操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Invoke((Action)(() => btnCalculate.Enabled = true));
            }
            finally
            {
                PerformanceTimer.Stop("任务模式计算");
            }
        }

        /// <summary>
        /// 计算报文模式的Offset（使用OffsetCalculatorBased策略）
        /// </summary>
        private void CalculateMessageOffsets(List<TaskItem> validTasks, int timeBaseUs)
        {
            PerformanceTimer.Start("报文模式计算");
            
            try
            {
                UpdateStatusBar("正在使用OffsetCalculator计算报文最优Offset...");

                // 禁用计算按钮，防止重复点击
                btnCalculate.Enabled = false;

                // 获取配置参数（在后台线程开始前获取）
                int maxOffsetUs = _appConfig.MainConfig.MaxOffsetUs;

                // 在后台线程中运行报文计算
                var messageCalculationTask = Task.Run(() =>
                {
                    bool calculationSuccess = false;
                    List<TaskItem> clonedTasks = null;
                    try
                    {
                        // 检查是否已被取消
                        if (_calculationForceStopRequested || _masterCancellationTokenSource?.IsCancellationRequested == true)
                        {
                            Console.WriteLine("🚫 报文计算在开始前被取消");
                            return;
                        }

                        // 创建任务副本以避免在后台线程中修改与UI绑定的原始任务对象
                        clonedTasks = TaskUtility.CloneTasks(validTasks);

                        // 创建OffsetCalculator实例
                        var calculator = new OffsetCalculator(timeBaseUs, maxOffsetUs);

                        // 计算最优Offset - 开启二分查找模式以找到最小的最优offset集
                        calculationSuccess = calculator.CalculateOptimalOffsets(clonedTasks, enableMinOffsetSearch: true);

                        if (!calculationSuccess)
                        {
                            throw new Exception("OffsetCalculator未能找到有效的解决方案");
                        }

                        // 报文计算完成
                    }
                    catch (Exception ex)
                    {
                        // 在UI线程中显示错误
                        this.Invoke((Action)(() =>
                        {
                            MessageBox.Show($"报文计算执行出错: {ex.Message}", "错误",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                            UpdateStatusBar("报文计算失败");
                            btnCalculate.Enabled = true;
                        }));
                        return;
                    }

                    // 在UI线程中处理结果
                    this.Invoke((Action)(() =>
                    {
                        try
                        {
                            // 应用计算结果到原始任务列表
                            ApplyMessageCalculationResults(clonedTasks, validTasks);
                            
                            // 计算完成后保存配置
                            UpdateConfigFromUI();
                            _dataIO.AutoSaveConfig();
                        }
                        finally
                        {
                            btnCalculate.Enabled = true;
                        }
                    }));
                });

                // 将报文计算任务添加到跟踪列表
                AddBackgroundTask(messageCalculationTask);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"报文计算操作错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Invoke((Action)(() => btnCalculate.Enabled = true));
            }
            finally
            {
                PerformanceTimer.Stop("报文模式计算");
            }
        }

        /// <summary>
        /// 应用报文计算结果
        /// </summary>
        private void ApplyMessageCalculationResults(List<TaskItem> clonedTasks, List<TaskItem> originalTasks)
        {
            try
            {
                int appliedCount = 0;
                int totalOffsetVariance = 0;
                
                // 创建TaskIndex到计算结果的映射
                var offsetResultMap = clonedTasks.Where(t => t.OffsetUs.HasValue)
                    .ToDictionary(t => t.TaskIndex, t => t.OffsetUs.Value);

                // 将计算结果应用到原始任务 - 保护手动输入的任务
                foreach (var originalTask in originalTasks)
                {
                    if (originalTask.ManualInput)
                    {
                        continue;
                    }
                    
                    if (offsetResultMap.TryGetValue(originalTask.TaskIndex, out var calculatedOffset))
                    {
                        originalTask.OffsetUs = calculatedOffset;
                        appliedCount++;
                        totalOffsetVariance += calculatedOffset;
                    }
                }

                // 统计已有offset结果
                foreach (var task in originalTasks)
                {
                    if (task.OffsetUs.HasValue && !offsetResultMap.ContainsKey(task.TaskIndex))
                    {
                        totalOffsetVariance += task.OffsetUs.Value;
                    }
                }

                // 计算负载方差作为报文分布质量指标
                double averageOffset = originalTasks.Count > 0 ? (double)totalOffsetVariance / originalTasks.Count : 0;
                double variance = 0;
                foreach (var task in originalTasks)
                {
                    if (task.OffsetUs.HasValue)
                    {
                        double diff = task.OffsetUs.Value - averageOffset;
                        variance += diff * diff;
                    }
                }
                variance = originalTasks.Count > 1 ? variance / (originalTasks.Count - 1) : 0;

                // 更新界面数据 - 通过UpdateValidTasksOffset方法正确更新
                _dgvManager.UpdateValidTasksOffset(originalTasks);

                // 生成简化的报告
                string report = $"报文Offset计算完成！\n\n";
                report += $"计算任务数: {appliedCount}/{originalTasks.Count}\n";
                report += $"平均Offset: {TaskUtility.ConvertToMilliseconds((int)averageOffset):F3} ms, ";
                report += $"Offset方差: {TaskUtility.ConvertToMilliseconds((int)Math.Sqrt(variance)):F3} \n\n";

                // 显示结果
                UpdateStatusBar($"报文Offset计算完成: {appliedCount}个任务, Offset方差={TaskUtility.ConvertToMilliseconds((int)Math.Sqrt(variance)):F3}");

                MessageBox.Show(report);

            }
            catch (Exception ex)
            {
                UpdateStatusBar("应用报文计算结果失败");
                MessageBox.Show($"应用报文计算结果时出错: {ex.Message}", "错误", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 过滤有效任务
        /// </summary>
        /// <param name="allTasks">所有任务</param>
        /// <returns>有效任务列表</returns>
        private List<TaskItem> FilterValidTasks(List<TaskItem> allTasks)
        {
            var validTasks = new List<TaskItem>();

            foreach (var task in allTasks)
            {
                // 检查任务是否使能
                if (!task.Enabled)
                {
                    continue;
                }

                // 检查任务名称
                if (string.IsNullOrWhiteSpace(task.TaskName))
                {
                    continue;
                }

                // 检查周期
                if (!task.PeriodUs.HasValue || task.PeriodUs.Value <= 0)
                {
                    continue;
                }

                // 检查执行时间不能超过周期
                if (task.ExecutionTimeUs.HasValue && task.ExecutionTimeUs.Value > task.PeriodUs.Value)
                {
                    continue;
                }

                // 任务通过所有检查，添加到有效列表
                // 重要：保持原始TaskIndex不变，确保索引的唯一性和一致性
                validTasks.Add(task);
            }

            return validTasks;
        }

        /// <summary>
        /// 处理智能调度结果
        /// </summary>
        private void ProcessIntelligentSchedulingResult(IntelligentSchedulingResult schedulingResult,
            List<TaskItem> validTasks, int timeBaseUs)
        {
            if (schedulingResult.Success && schedulingResult.FinalBestResult != null)
            {
                // 应用智能调度的结果到任务列表
                if (schedulingResult.FinalBestResult.OptimizedTasks != null)
                {

                    // 创建TaskIndex到优化任务的映射，保持索引的一致性
                    // 使用GroupBy避免重复TaskIndex导致的异常
                    var optimizedTaskMap = schedulingResult.FinalBestResult.OptimizedTasks
                        .GroupBy(t => t.TaskIndex)
                        .ToDictionary(g => g.Key, g => g.First());

                    // 更新原任务列表的Offset值 - 保护手动输入的任务
                    foreach (var originalTask in validTasks)
                    {
                        if (originalTask.ManualInput)
                        {
                            continue;
                        }
                        
                        if (optimizedTaskMap.TryGetValue(originalTask.TaskIndex, out var optimizedTask))
                        {
                            // 将优化结果的微秒值转换为毫秒并赋值给原任务
                            if (optimizedTask.OffsetUs.HasValue)
                            {
                                originalTask.OffsetUs = optimizedTask.OffsetUs.Value;
                            }
                        }
                    }
                }

                // 更新任务列表显示 - 只更新有效任务的Offset，保留所有原始任务
                _dgvManager.UpdateValidTasksOffset(validTasks);

                // 从智能调度结果获取优化信息
                var stats = schedulingResult.FinalBestResult.SchedulabilityResult?.Statistics;
                var finalMaxOffset = stats?.MaxOffsetUs != null ? TaskUtility.ConvertToMilliseconds(stats.MaxOffsetUs) : 0;
                var maxResponseRatio = stats?.MaxResponseTimeRatio ?? 0;
                var avgResponseRatio = stats?.AverageResponseTimeRatio ?? 0;

                // 更新状态栏
                double timeBaseMs = TaskUtility.ConvertToMilliseconds(timeBaseUs);
                UpdateStatusBar($"智能调度完成 (时基: {timeBaseMs:F3}ms), 最优最大Offset：{finalMaxOffset:F1}ms, 响应时间比率：{maxResponseRatio:F3}");

                // 显示智能调度报告
                var reportBuilder = new StringBuilder();
                reportBuilder.AppendLine("=== 智能调度计算报告 ===");
                reportBuilder.AppendLine($"算法选择: {schedulingResult.FinalBestResult.Algorithm}");
                reportBuilder.AppendLine($"测试的Offset序列: {schedulingResult.OffsetSequence.Count}个");
                reportBuilder.AppendLine($"执行的优化步骤: {schedulingResult.StepResults.Count}个");
                reportBuilder.AppendLine($"总优化时间: {schedulingResult.TotalTime.TotalSeconds:F2}秒");

                // 新增统一选择机制说明
                reportBuilder.AppendLine();
                reportBuilder.AppendLine("=== 统一选择机制 ===");
                reportBuilder.AppendLine("采用统一候选集合评估方式：");
                reportBuilder.AppendLine("1. 收集SA和GA所有成功策略结果到统一集合");
                reportBuilder.AppendLine("2. 先筛选最佳成本的候选结果（容差1e-6）");
                reportBuilder.AppendLine("3. 基于统一集合应用新的排序规则选择最终解");
                reportBuilder.AppendLine("4. 新选择逻辑：动态忙比例 → 启动重叠率 → 最大offset → 静态重叠率 → 冲突率 → 算法/策略");

                // 检查是否使用了多起点策略
                if (schedulingResult.Parameters?.UseMultiStartStrategies == true)
                {
                    reportBuilder.AppendLine();
                    reportBuilder.AppendLine($"多起点策略: 启用 (基础策略: {schedulingResult.Parameters.UseBasicStrategies}, 扩展策略: {schedulingResult.Parameters.UseExtendedStrategies})");
                    reportBuilder.AppendLine($"多起点选择逻辑: 直接选择最佳成本（无容差）");
                }
                else
                {
                    reportBuilder.AppendLine();
                    reportBuilder.AppendLine("多起点策略: 未启用");
                }

                reportBuilder.AppendLine();
                reportBuilder.AppendLine("=== 最优解详情 ===");
                reportBuilder.AppendLine($"选择算法: {schedulingResult.FinalBestResult.Algorithm}");

                // 尝试从多起点详情中获取策略信息
                string selectedStrategy = "未知策略";
                if (schedulingResult.FinalBestResult.MultiStartDetails != null)
                {
                    selectedStrategy = schedulingResult.FinalBestResult.MultiStartDetails.BestStrategy.ToString();
                }
                reportBuilder.AppendLine($"选择策略: {selectedStrategy}");

                    // 直接复用挑选阶段缓存的指标（避免重复计算）
                    var finalSolution = ExtractOffsetSolution(schedulingResult.FinalBestResult);
                    double finalOverlapRate = 0.0; // 默认值，用于传递给HTML生成器
                    var extra = schedulingResult.FinalBestResult.ExtraMetrics;
                    // 统一选择路径下，ExtraMetrics应始终存在
                    finalOverlapRate = extra != null ? extra.StaticOverlapRate : 0.0;
                    reportBuilder.AppendLine($"最终成本: {schedulingResult.FinalBestResult.BestCost:F6}");
                    if (extra != null)
                    {
                        reportBuilder.AppendLine($"冲突率: {extra.ConflictRate:F3}");
                        reportBuilder.AppendLine($"重叠率: {extra.StaticOverlapRate:F3} (静态综合)");
                        reportBuilder.AppendLine($"启动重叠率: {extra.StartOverlapRate:F3}");
                        reportBuilder.AppendLine($"动态忙比例: {extra.DynamicBusyAtStartRatio:F3}");
                        reportBuilder.AppendLine($"启动聚集度: {extra.StartClusteringScore:F4}");
                        reportBuilder.AppendLine($"峰值启动并发: {extra.PeakStartConcurrency}");
                        reportBuilder.AppendLine($"启动并发方差: {extra.StartConcurrencyVariance:F4}");
                        reportBuilder.AppendLine($"优先级对齐成本: {extra.PriorityAlignmentCost:F6}");
                        reportBuilder.AppendLine($"选择逻辑: 动态忙比例({extra.DynamicBusyAtStartRatio:F3}) → 启动聚集度({extra.StartClusteringScore:F4}) → 启动重叠率({extra.StartOverlapRate:F3}) → 优先级对齐成本({extra.PriorityAlignmentCost:F6}) → 最大offset({TaskUtility.ConvertToMilliseconds(extra.MaxOffsetUs):F3}ms) → 静态重叠率({extra.StaticOverlapRate:F3}) → 冲突率({extra.ConflictRate:F3})");
                    }

                reportBuilder.AppendLine($"最优最大Offset: {finalMaxOffset:F1}ms");
                reportBuilder.AppendLine($"最大响应时间比率: {maxResponseRatio:F3}");
                reportBuilder.AppendLine($"平均响应时间比率: {avgResponseRatio:F3}");
                if (stats != null)
                {
                    reportBuilder.AppendLine($"错过截止时间任务数: {stats.DeadlineMissCount}");
                    reportBuilder.AppendLine($"响应时间比率方差: {stats.ResponseTimeRatioVariance:F6}");
                }
                reportBuilder.AppendLine();
                reportBuilder.AppendLine("=== 任务Offset配置 ===");

                // 只显示有效任务的Offset配置
                foreach (var task in validTasks.OrderBy(t => t.TaskName))
                {
                    var offsetMs = task.OffsetUs.HasValue ? TaskUtility.ConvertToMilliseconds(task.OffsetUs.Value) : 0.0;
                    reportBuilder.AppendLine($"{task.TaskName}: {offsetMs:F1}ms");
                }

                // 如果启用了多起点策略，显示最终选择的offset下的策略详细结果
                if (schedulingResult.Parameters?.UseMultiStartStrategies == true)
                {
                    reportBuilder.AppendLine();
                    reportBuilder.AppendLine("=== 多起点策略详细结果 ===");

                    // 找到最终最优结果对应的步骤
                    var finalBestStep = schedulingResult.StepResults?
                        .FirstOrDefault(step => step.BestResult == schedulingResult.FinalBestResult);

                    if (finalBestStep != null)
                    {
                        double finalBestStepMaxOffsetMs = TaskUtility.ConvertToMilliseconds(finalBestStep.MaxOffsetUs);
                        reportBuilder.AppendLine($"最终选择的最大Offset: {finalBestStepMaxOffsetMs:F1}ms");
                        reportBuilder.AppendLine();

                        // **新增：双轨优化对比显示**
                        reportBuilder.AppendLine("=== 双轨优化算法对比 ===");
                        
                        bool foundMultiStartResults = false;
                        
                        // 显示模拟退火结果
                        if (finalBestStep.SimulatedAnnealingResult != null)
                        {
                            var saResult = finalBestStep.SimulatedAnnealingResult;
                            reportBuilder.AppendLine($"模拟退火算法:");
                            reportBuilder.AppendLine($"  算法类型: {saResult.Algorithm}");
                            reportBuilder.AppendLine($"  执行状态: {(saResult.Success ? "成功" : "失败")}");
                            if (saResult.Success)
                            {
                                reportBuilder.AppendLine($"  最佳成本: {saResult.BestCost:F6}");
                                reportBuilder.AppendLine($"  执行时间: {saResult.OptimizationTime.TotalMilliseconds:F0}ms");
                                reportBuilder.AppendLine($"  总迭代数: {saResult.TotalIterations}");
                                
                                // 显示可调度性统计
                                if (saResult.SchedulabilityResult?.Statistics != null)
                                {
                                    var saStats = saResult.SchedulabilityResult.Statistics;
                                    reportBuilder.AppendLine($"  响应时间比率: 最大={saStats.MaxResponseTimeRatio:F3}, 平均={saStats.AverageResponseTimeRatio:F3}");
                                    reportBuilder.AppendLine($"  错过截止时间: {saStats.DeadlineMissCount}个任务");
                                }
                            }
                            else
                            {
                                reportBuilder.AppendLine($"  失败原因: {saResult.ErrorMessage}");
                            }
                            
                            // **添加SA多起点策略详细显示**
                            reportBuilder.AppendLine();
                            if (saResult.MultiStartDetails != null && 
                                saResult.MultiStartDetails.StrategyResults != null && 
                                saResult.MultiStartDetails.StrategyResults.Count > 0)
                            {
                                foundMultiStartResults = true;
                                reportBuilder.AppendLine($"模拟退火多起点策略:");
                                reportBuilder.AppendLine($"  最佳策略: {saResult.MultiStartDetails.BestStrategy}");
                                reportBuilder.AppendLine($"  最佳成本: {saResult.MultiStartDetails.BestCost:F6}");
                                reportBuilder.AppendLine($"  总执行时间: {saResult.MultiStartDetails.OptimizationTime.TotalMilliseconds:F0}ms");
                                reportBuilder.AppendLine($"  使用策略数: {saResult.MultiStartDetails.TotalStrategiesUsed}");

                                // 计算所有策略的统计信息，用于综合分数计算
                                var allSAStrategiesStats = saResult.MultiStartDetails.StrategyResults
                                    .Where(sr => sr.Success)
                                    .Select(sr => IntelligentSchedulingEngine.CalculateOffsetStatistics(sr.FinalSolution))
                                    .ToList();

                                // **显示所有策略的结果，包括失败的策略**
                                var successfulSAStrategies = saResult.MultiStartDetails.StrategyResults.Where(sr => sr.Success).ToList();
                                var failedSAStrategies = saResult.MultiStartDetails.StrategyResults.Where(sr => !sr.Success).ToList();
                                
                                reportBuilder.AppendLine($"  成功策略数: {successfulSAStrategies.Count}/{saResult.MultiStartDetails.StrategyResults.Count}");
                                if (failedSAStrategies.Count > 0)
                                {
                                    reportBuilder.AppendLine($"  失败策略数: {failedSAStrategies.Count}");
                                    reportBuilder.AppendLine($"  失败策略: {string.Join(", ", failedSAStrategies.Select(s => s.Strategy))}");
                                }

                                // 显示各策略对比结果
                                foreach (var strategyResult in saResult.MultiStartDetails.StrategyResults)
                                {
                                    reportBuilder.AppendLine($"\n  策略: {strategyResult.Strategy}");
                                    if (strategyResult.Success)
                                    {
                                        var strategyStats = IntelligentSchedulingEngine.CalculateOffsetStatistics(strategyResult.FinalSolution);
                                        var conflictRate = IntelligentSchedulingEngine.CalculateConflictRate(strategyResult.FinalSolution);
                                        var overlapRate = IntelligentSchedulingEngine.CalculateOverlapRate(strategyResult.FinalSolution, validTasks);

                                        // 计算“启动重叠率(静态)”与“动态忙比例”
                                        double startOverlapRate = 0.0;
                                        double dynamicBusyRatio = 0.0;
                                        double priorityAlignCost = 0.0;
                                        double startClusteringScore = 0.0;
                                        try
                                        {
                                            // 基于该策略最终解，克隆任务并应用offset
                                            var tasksForMetric = TaskUtility.CloneTasks(validTasks);
                                            if (strategyResult.FinalSolution != null)
                                            {
                                                foreach (var tfm in tasksForMetric)
                                                {
                                                    int newOffset;
                                                    if (strategyResult.FinalSolution.TryGetValue(tfm.TaskIndex, out newOffset))
                                                    {
                                                        tfm.OffsetUs = newOffset;
                                                    }
                                                }
                                            }
                                            startOverlapRate = SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(tasksForMetric);
                                            dynamicBusyRatio = IntelligentSchedulingEngine.CalculateDynamicBusyAtStartRatioStatic(timeBaseUs, tasksForMetric);
                                            // 计算优先级对齐成本（轻量WCRT分析，不做动态模拟）
                                            var analyzer = new SchedulabilityAnalyzer(timeBaseUs);
                                            var sched = analyzer.AnalyzeSchedulability(tasksForMetric);
                                            priorityAlignCost = IntelligentSchedulingEngine.CalculatePriorityAlignmentCostForTasks(sched?.TaskResults);
                                            // 启动聚集度（轻量静态统计）
                                            int peakTmp;
                                            double varTmp;
                                            startClusteringScore = IntelligentSchedulingEngine.CalculateStartClusteringScore(timeBaseUs, tasksForMetric, out peakTmp, out varTmp);
                                        }
                                        catch { }

                                        reportBuilder.AppendLine($"    成本: {strategyResult.FinalCost:F6}");
                                        reportBuilder.AppendLine($"    改进比例: {strategyResult.ImprovementRatio:P2}");
                                        reportBuilder.AppendLine($"    最佳优化器: {strategyResult.BestOptimizer}");
                                        reportBuilder.AppendLine($"    统计: 均值={strategyStats.Mean:F3}ms, 方差={strategyStats.Variance:F3}ms², 标准差={strategyStats.StdDev:F3}ms");
                                        reportBuilder.AppendLine($"    冲突率: {conflictRate:F3}");
                                        reportBuilder.AppendLine($"    重叠率: {overlapRate:F3}");
                                        reportBuilder.AppendLine($"    启动重叠率: {startOverlapRate:F3}");
                                        reportBuilder.AppendLine($"    动态忙比例: {dynamicBusyRatio:F3}");
                                        if (allSAStrategiesStats.Count > 1)
                                        {
                                            // 使用新的选择逻辑说明（加入启动聚集度与优先级对齐成本）
                                            int maxOffsetUsStrategy = (strategyResult.FinalSolution != null && strategyResult.FinalSolution.Count > 0)
                                                ? strategyResult.FinalSolution.Values.Max()
                                                : 0;
                                            reportBuilder.AppendLine($"    新选择逻辑: 动态忙比例({dynamicBusyRatio:F3}) -> 启动聚集度({startClusteringScore:F4}) -> 启动重叠率({startOverlapRate:F3}) -> 优先级对齐成本({priorityAlignCost:F4}) -> 最大offset({TaskUtility.ConvertToMilliseconds(maxOffsetUsStrategy):F3}ms) -> 静态重叠率({overlapRate:F3}) -> 冲突率({conflictRate:F3})");
                                        }

                                        // 显示具体任务offset配置
                                        reportBuilder.AppendLine($"    具体任务offset配置:");
                                        if (strategyResult.FinalSolution != null && strategyResult.FinalSolution.Count > 0)
                                        {
                                            var sortedTasks = validTasks.Where(t => strategyResult.FinalSolution.ContainsKey(t.TaskIndex))
                                                .OrderBy(t => t.TaskName).ToList();

                                            foreach (var task in sortedTasks)
                                            {
                                                if (strategyResult.FinalSolution.TryGetValue(task.TaskIndex, out int offsetUs))
                                                {
                                                    double offsetMs = TaskUtility.ConvertToMilliseconds(offsetUs);
                                                    reportBuilder.AppendLine($"      {task.TaskName}: {offsetMs:F3}ms");
                                                }
                                            }
                                        }
                                        else
                                        {
                                            reportBuilder.AppendLine($"      (无offset数据)");
                                        }
                                    }
                                    else
                                    {
                                        reportBuilder.AppendLine($"    状态: 失败");
                                        reportBuilder.AppendLine($"    失败原因: {strategyResult.ErrorMessage}");
                                        
                                        // **特别处理offset限制过小的情况**
                                        if (strategyResult.ErrorMessage.Contains("offset限制") && strategyResult.ErrorMessage.Contains("过小"))
                                        {
                                            double currentMaxOffsetMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.MaxOffsetUs);
                                            reportBuilder.AppendLine($"    当前最大Offset: {currentMaxOffsetMs:F1}ms");
                                            reportBuilder.AppendLine($"    建议: 尝试增加最大Offset限制以允许更多策略参与优化");
                                        }
                                    }
                                }
                            }
                        }
                        
                        reportBuilder.AppendLine();
                        
                        // 显示遗传算法结果
                        if (finalBestStep.GeneticAlgorithmResult != null)
                        {
                            var gaResult = finalBestStep.GeneticAlgorithmResult;
                            
                            if (gaResult.MultiStartDetails != null && 
                                gaResult.MultiStartDetails.StrategyResults != null && 
                                gaResult.MultiStartDetails.StrategyResults.Count > 0)
                            {
                                foundMultiStartResults = true;
                                reportBuilder.AppendLine($"遗传算法多起点策略:");
                                reportBuilder.AppendLine($"  算法状态: {(gaResult.Success ? "成功" : "失败")}");
                                
                                if (gaResult.Success)
                                {
                                    reportBuilder.AppendLine($"  最佳策略: {gaResult.MultiStartDetails.BestStrategy}");
                                    reportBuilder.AppendLine($"  最佳成本: {gaResult.MultiStartDetails.BestCost:F6}");
                                    reportBuilder.AppendLine($"  总执行时间: {gaResult.MultiStartDetails.OptimizationTime.TotalMilliseconds:F0}ms");
                                    reportBuilder.AppendLine($"  使用策略数: {gaResult.MultiStartDetails.TotalStrategiesUsed}");
                                }
                                else
                                {
                                    reportBuilder.AppendLine($"  失败原因: {gaResult.ErrorMessage}");
                                }

                                // **始终显示GA各策略的详细结果**
                                if (gaResult.MultiStartDetails.StrategyResults != null && gaResult.MultiStartDetails.StrategyResults.Count > 0)
                                {
                                    // 计算所有策略的统计信息，用于综合分数计算
                                    var allGAStrategiesStats = gaResult.MultiStartDetails.StrategyResults
                                        .Where(sr => sr.Success)
                                        .Select(sr => IntelligentSchedulingEngine.CalculateOffsetStatistics(sr.FinalSolution))
                                        .ToList();

                                    // **显示所有策略的结果，包括失败的策略**
                                    var successfulStrategies = gaResult.MultiStartDetails.StrategyResults.Where(sr => sr.Success).ToList();
                                    var failedStrategies = gaResult.MultiStartDetails.StrategyResults.Where(sr => !sr.Success).ToList();
                                    
                                    reportBuilder.AppendLine($"  成功策略数: {successfulStrategies.Count}/{gaResult.MultiStartDetails.StrategyResults.Count}");
                                    if (failedStrategies.Count > 0)
                                    {
                                        reportBuilder.AppendLine($"  失败策略数: {failedStrategies.Count}");
                                        reportBuilder.AppendLine($"  失败策略: {string.Join(", ", failedStrategies.Select(s => s.Strategy))}");
                                    }

                                    // 显示各策略对比结果
                                    foreach (var strategyResult in gaResult.MultiStartDetails.StrategyResults)
                                    {
                                        reportBuilder.AppendLine($"\n  策略: {strategyResult.Strategy}");
                                        if (strategyResult.Success)
                                        {
                                            var strategyStats = IntelligentSchedulingEngine.CalculateOffsetStatistics(strategyResult.FinalSolution);
                                            var conflictRate = IntelligentSchedulingEngine.CalculateConflictRate(strategyResult.FinalSolution);
                                            var overlapRate = IntelligentSchedulingEngine.CalculateOverlapRate(strategyResult.FinalSolution, validTasks);

                                            // 计算“启动重叠率(静态)”与“动态忙比例”
                                            double startOverlapRate = 0.0;
                                            double dynamicBusyRatio = 0.0;
                                            double priorityAlignCost = 0.0;
                                            double startClusteringScore = 0.0;
                                            try
                                            {
                                                var tasksForMetric = TaskUtility.CloneTasks(validTasks);
                                                if (strategyResult.FinalSolution != null)
                                                {
                                                    foreach (var tfm in tasksForMetric)
                                                    {
                                                        int newOffset;
                                                        if (strategyResult.FinalSolution.TryGetValue(tfm.TaskIndex, out newOffset))
                                                        {
                                                            tfm.OffsetUs = newOffset;
                                                        }
                                                    }
                                                }
                                                startOverlapRate = SchedulabilityAnalyzer.ComputeStartOverlapRateForTasks(tasksForMetric);
                                                dynamicBusyRatio = IntelligentSchedulingEngine.CalculateDynamicBusyAtStartRatioStatic(timeBaseUs, tasksForMetric);
                                                var analyzer = new SchedulabilityAnalyzer(timeBaseUs);
                                                var sched = analyzer.AnalyzeSchedulability(tasksForMetric);
                                                priorityAlignCost = IntelligentSchedulingEngine.CalculatePriorityAlignmentCostForTasks(sched?.TaskResults);
                                                int peakTmp;
                                                double varTmp;
                                                startClusteringScore = IntelligentSchedulingEngine.CalculateStartClusteringScore(timeBaseUs, tasksForMetric, out peakTmp, out varTmp);
                                            }
                                            catch { }

                                            reportBuilder.AppendLine($"    成本: {strategyResult.FinalCost:F6}");
                                            reportBuilder.AppendLine($"    改进比例: {strategyResult.ImprovementRatio:P2}");
                                            reportBuilder.AppendLine($"    最佳优化器: {strategyResult.BestOptimizer}");
                                            reportBuilder.AppendLine($"    统计: 均值={strategyStats.Mean:F3}ms, 方差={strategyStats.Variance:F3}ms², 标准差={strategyStats.StdDev:F3}ms");
                                            reportBuilder.AppendLine($"    冲突率: {conflictRate:F3}");
                                            reportBuilder.AppendLine($"    重叠率: {overlapRate:F3}");
                                            reportBuilder.AppendLine($"    启动重叠率: {startOverlapRate:F3}");
                                            reportBuilder.AppendLine($"    动态忙比例: {dynamicBusyRatio:F3}");
                                            if (allGAStrategiesStats.Count > 1)
                                            {
                                                // 使用新的选择逻辑说明（加入启动聚集度与优先级对齐成本）
                                                int maxOffsetUsStrategy = (strategyResult.FinalSolution != null && strategyResult.FinalSolution.Count > 0)
                                                    ? strategyResult.FinalSolution.Values.Max()
                                                    : 0;
                                                reportBuilder.AppendLine($"    新选择逻辑: 动态忙比例({dynamicBusyRatio:F3}) -> 启动聚集度({startClusteringScore:F4}) -> 启动重叠率({startOverlapRate:F3}) -> 优先级对齐成本({priorityAlignCost:F4}) -> 最大offset({TaskUtility.ConvertToMilliseconds(maxOffsetUsStrategy):F3}ms) -> 静态重叠率({overlapRate:F3}) -> 冲突率({conflictRate:F3})");
                                            }

                                            // 显示具体任务offset配置
                                            reportBuilder.AppendLine($"    具体任务offset配置:");
                                            if (strategyResult.FinalSolution != null && strategyResult.FinalSolution.Count > 0)
                                            {
                                                var sortedTasks = validTasks.Where(t => strategyResult.FinalSolution.ContainsKey(t.TaskIndex))
                                                    .OrderBy(t => t.TaskName).ToList();

                                                foreach (var task in sortedTasks)
                                                {
                                                    if (strategyResult.FinalSolution.TryGetValue(task.TaskIndex, out int offsetUs))
                                                    {
                                                        double offsetMs = TaskUtility.ConvertToMilliseconds(offsetUs);
                                                        reportBuilder.AppendLine($"      {task.TaskName}: {offsetMs:F3}ms");
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                reportBuilder.AppendLine($"      (无offset数据)");
                                            }
                                        }
                                        else
                                        {
                                            reportBuilder.AppendLine($"    状态: 失败");
                                            reportBuilder.AppendLine($"    失败原因: {strategyResult.ErrorMessage}");
                                            
                                            // **特别处理offset限制过小的情况**
                                            if (strategyResult.ErrorMessage.Contains("offset限制") && strategyResult.ErrorMessage.Contains("过小"))
                                            {
                                                double currentMaxOffsetMs = TaskUtility.ConvertToMilliseconds(_appConfig.MainConfig.MaxOffsetUs);
                                                reportBuilder.AppendLine($"    当前最大Offset: {currentMaxOffsetMs:F1}ms");
                                                reportBuilder.AppendLine($"    建议: 尝试增加最大Offset限制以允许更多策略参与优化");
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    reportBuilder.AppendLine($"  (无策略详细结果)");
                                }
                            }
                            else
                            {
                                // GA未使用多起点策略或无详细结果
                                reportBuilder.AppendLine($"遗传算法策略:");
                                reportBuilder.AppendLine($"  算法类型: {gaResult.Algorithm}");
                                reportBuilder.AppendLine($"  执行状态: {(gaResult.Success ? "成功" : "失败")}");
                                reportBuilder.AppendLine($"  说明: GA使用了单起点策略，而非多起点策略");
                                if (gaResult.Success)
                                {
                                    reportBuilder.AppendLine($"  最佳成本: {gaResult.BestCost:F6}");
                                    reportBuilder.AppendLine($"  执行时间: {gaResult.OptimizationTime.TotalMilliseconds:F0}ms");
                                    
                                    // 显示单起点GA的offset配置
                                    if (gaResult.OptimizedTasks != null && gaResult.OptimizedTasks.Count > 0)
                                    {
                                        reportBuilder.AppendLine($"  具体任务offset配置:");
                                        foreach (var task in gaResult.OptimizedTasks.Where(t => t.OffsetUs.HasValue).OrderBy(t => t.TaskName))
                                        {
                                            double offsetMs = TaskUtility.ConvertToMilliseconds(task.OffsetUs.Value);
                                            reportBuilder.AppendLine($"    {task.TaskName}: {offsetMs:F3}ms");
                                        }
                                    }
                                }
                                else
                                {
                                    reportBuilder.AppendLine($"  失败原因: {gaResult.ErrorMessage}");
                                }
                            }
                        }
                        else
                        {
                            reportBuilder.AppendLine($"遗传算法策略: 未执行");
                        }

                        if (!foundMultiStartResults)
                        {
                            reportBuilder.AppendLine("\n注意: 未发现多起点策略结果或算法执行失败");
                        }
                    }
                    else
                    {
                        reportBuilder.AppendLine("未找到最终最优结果对应的步骤详情");
                    }
                }

                // 添加统一选择过程详细信息
                if (schedulingResult.StepResults != null && schedulingResult.StepResults.Count > 0)
                {
                    reportBuilder.AppendLine();
                    reportBuilder.AppendLine("=== 统一选择过程详情 ===");

                    var totalCandidates = 0;
                    var algorithmsUsed = new HashSet<string>();

                    foreach (var step in schedulingResult.StepResults)
                    {
                        if (step.SimulatedAnnealingResult?.Success == true)
                        {
                            if (step.SimulatedAnnealingResult.MultiStartDetails?.StrategyResults != null)
                            {
                                var successfulSAStrategies = step.SimulatedAnnealingResult.MultiStartDetails.StrategyResults.Count(sr => sr.Success);
                                totalCandidates += successfulSAStrategies;
                                algorithmsUsed.Add("模拟退火(多起点)");
                            }
                            else
                            {
                                totalCandidates += 1;
                                algorithmsUsed.Add("模拟退火(单起点)");
                            }
                        }

                        if (step.GeneticAlgorithmResult?.Success == true)
                        {
                            if (step.GeneticAlgorithmResult.MultiStartDetails?.StrategyResults != null)
                            {
                                var successfulGAStrategies = step.GeneticAlgorithmResult.MultiStartDetails.StrategyResults.Count(sr => sr.Success);
                                totalCandidates += successfulGAStrategies;
                                algorithmsUsed.Add("遗传算法(多起点)");
                            }
                            else
                            {
                                totalCandidates += 1;
                                algorithmsUsed.Add("遗传算法(单起点)");
                            }
                        }
                    }

                    reportBuilder.AppendLine($"候选策略总数: {totalCandidates}个");
                    reportBuilder.AppendLine($"参与算法: {string.Join(", ", algorithmsUsed)}");
                    reportBuilder.AppendLine($"选择逻辑: 先筛选最佳成本（容差1e-6），再按新排序挑选");
                    reportBuilder.AppendLine($"评估维度: 动态忙比例 → 启动聚集度 → 启动重叠率 → 优先级对齐成本 → 最大offset → 静态重叠率 → 冲突率 → 算法/策略");
                    reportBuilder.AppendLine($"最终选择: {schedulingResult.FinalBestResult.Algorithm}");
                }

                if (schedulingResult.OptimizationDecision != null)
                {
                    reportBuilder.AppendLine();
                    reportBuilder.AppendLine(schedulingResult.OptimizationDecision.DecisionSummary);
                }

                // 显示智能调度报告对话框
                ShowTestReportDialog(reportBuilder.ToString(), "智能调度计算报告");

                // 自动执行校验并显示校验报告
                var offsetValidationResult = ExecuteValidationAndShowReport(validTasks, timeBaseUs);
                // 将挑选阶段指标透传给校验结果，供Timeline/HTML复用
                try { offsetValidationResult.ExtraMetrics = schedulingResult?.FinalBestResult?.ExtraMetrics; } catch { }

                // 生成时间轴网页（仅在可调度时生成，避免错误图表）
                if (offsetValidationResult?.IsSchedulable == true)
                {
                    try
                    {
                        // TimelineGenerator.GenerateTimelineHtml期望的是微秒(us)单位，不需要转换
                        TimelineGenerator.GenerateTimelineHtml(validTasks, timeBaseUs, "us", offsetValidationResult,
                            TaskUtility.ConvertToMicroseconds(finalMaxOffset), schedulingResult.StepResults.Count,
                            $"{maxResponseRatio:P2}", stats?.ResponseTimeRatioVariance ?? 0, 0, finalOverlapRate);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("智能调度完成！偏移量校验报告已生成。\n网页生成失败，请查看日志输出。",
                            "计算完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            else
            {
                // 智能调度失败
                UpdateStatusBar("智能调度失败");
                var errorMsg = !string.IsNullOrEmpty(schedulingResult.ErrorMessage)
                    ? schedulingResult.ErrorMessage
                    : "智能调度引擎未能找到可行的Offset配置";
                MessageBox.Show($"智能调度失败：\n\n{errorMsg}",
                    "计算失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 从优化结果中提取offset解决方案
        /// </summary>
        /// <param name="result">优化结果</param>
        /// <returns>offset字典（微秒值）</returns>
        private Dictionary<int, int> ExtractOffsetSolution(OptimizationResultSummary result)
        {
            var solution = new Dictionary<int, int>();
            if (result?.OptimizedTasks != null)
            {
                foreach (var task in result.OptimizedTasks)
                {
                    if (task.OffsetUs.HasValue)
                    {
                        // 直接使用微秒值，保持与IntelligentSchedulingEngine一致
                        solution[task.TaskIndex] = task.OffsetUs.Value;
                    }
                }
            }
            return solution;
        }

        /// <summary>
        /// 执行校验并显示报告，返回校验结果
        /// </summary>
        private ValidationResult ExecuteValidationAndShowReport(List<TaskItem> tasks, int timeBaseUs)
        {
            // 过滤出已设置了Offset的任务
            var tasksWithOffset = tasks.Where(t => t.OffsetUs.HasValue && t.PeriodUs.HasValue && t.PeriodUs.Value > 0).ToList();

            if (tasksWithOffset.Count == 0)
            {
                MessageBox.Show("没有可校验的任务，跳过校验步骤。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                UpdateStatusBar("没有可校验的任务");
                return new ValidationResult { IsValid = true, Message = "无需校验" };
            }

            // 创建校验器
            OffsetValidator validator = new OffsetValidator(timeBaseUs);

            // 校验偏移量
            var result = validator.ValidateOffsets(tasksWithOffset);

            // 生成校验报告
            string report = validator.GenerateValidationReport(result);

            // 显示校验结果对话框
            ShowValidationReportDialog(report, result);

            // 更新状态栏
            UpdateStatusBar($"已完成偏移量校验{(result.IsValid ? "，结果有效" : "，发现问题")}");

            return result;
        }

        /// <summary>
        /// 显示校验报告对话框
        /// </summary>
        private void ShowValidationReportDialog(string report, ValidationResult result)
        {
            using (var form = new Form())
            {
                form.Text = "偏移量校验报告";
                form.Size = new System.Drawing.Size(900, 600);  // 增加宽度从600到900，高度从500到600
                form.MinimumSize = new System.Drawing.Size(800, 400);  // 设置最小尺寸
                form.StartPosition = FormStartPosition.CenterParent;
                form.MinimizeBox = false;
                form.MaximizeBox = true;  // 允许最大化
                form.ShowIcon = false;
                form.ShowInTaskbar = false;
                form.FormBorderStyle = FormBorderStyle.Sizable;  // 改为可调整大小

                // 创建RichTextBox显示报告
                var richTextBox = new RichTextBox();
                richTextBox.Multiline = true;
                richTextBox.ReadOnly = true;
                richTextBox.ScrollBars = RichTextBoxScrollBars.Vertical;
                richTextBox.Dock = DockStyle.Fill;
                richTextBox.Font = new System.Drawing.Font("Consolas", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
                richTextBox.Padding = new Padding(0, 0, 0, 50);  // 为关闭按钮留出底部空间

                // 将报告文本按行分割
                string[] lines = report.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);

                // 逐行添加文本
                foreach (string line in lines)
                {
                    // 定位到结尾
                    richTextBox.SelectionStart = richTextBox.TextLength;
                    richTextBox.SelectionLength = 0;

                    // 根据内容设置颜色
                    if (line.StartsWith("✓"))
                    {
                        richTextBox.SelectionColor = Color.Green;
                    }
                    else if (line.Contains("✗ 不可调度"))
                    {
                        // 不可调度显示红色
                        richTextBox.SelectionColor = Color.Red;
                    }
                    else if (line.Contains("CPU利用率:") || line.Contains("CPU 负载率:"))
                    {
                        // 解析CPU利用率
                        try
                        {
                            string searchPattern = line.Contains("CPU利用率:") ? "CPU利用率:" : "CPU 负载率:";
                            int startIndex = line.IndexOf(searchPattern) + searchPattern.Length;
                            int endIndex = line.IndexOf("%", startIndex);
                            if (endIndex > startIndex)
                            {
                                string cpuUsageStr = line.Substring(startIndex, endIndex - startIndex).Trim();
                                if (double.TryParse(cpuUsageStr, out double cpuUsage))
                                {
                                    // CPU利用率>100%显示红色，100%显示橙色，>70%显示橙色，否则显示绿色
                                    if (cpuUsage > 100.0)
                                    {
                                        richTextBox.SelectionColor = Color.Red;
                                    }
                                    else if (cpuUsage >= 90.0)
                                    {
                                        richTextBox.SelectionColor = Color.Orange;
                                    }
                                    else if (cpuUsage > 70.0)
                                    {
                                        richTextBox.SelectionColor = Color.DarkOrange;
                                    }
                                    else
                                    {
                                        richTextBox.SelectionColor = Color.Green;
                                    }
                                }
                                else
                                {
                                    richTextBox.SelectionColor = richTextBox.ForeColor;
                                }
                            }
                            else
                            {
                                richTextBox.SelectionColor = richTextBox.ForeColor;
                            }
                        }
                        catch
                        {
                            richTextBox.SelectionColor = richTextBox.ForeColor;
                        }
                    }
                    else if (line.Contains("可调度性:"))
                    {
                        // 可调度性行的颜色处理
                        if (line.Contains("✓ 可调度"))
                        {
                            richTextBox.SelectionColor = Color.Green;
                        }
                        else if (line.Contains("✗ 不可调度"))
                        {
                            richTextBox.SelectionColor = Color.Red;
                        }
                        else
                        {
                            richTextBox.SelectionColor = richTextBox.ForeColor;
                        }
                    }
                    else if (line.Contains("偏移量校验通过") || line.StartsWith("✓ 偏移量校验通过"))
                    {
                        // 验证结论成功的颜色处理 - 与可调度性保持一致
                        richTextBox.SelectionColor = Color.Green;
                    }
                    else if (line.Contains("截止时间违反") || line.Contains("系统成本过高") || 
                             line.Contains("系统不可调度") || line.StartsWith("✗"))
                    {
                        // 验证结论失败的颜色处理 - 与可调度性保持一致
                        richTextBox.SelectionColor = Color.Red;
                    }
                    else
                    {
                        // 恢复默认颜色
                        richTextBox.SelectionColor = richTextBox.ForeColor;
                    }

                    // 添加文本和换行
                    richTextBox.AppendText(line + "\n");
                }

                // 滚动到顶部
                richTextBox.SelectionStart = 0;
                richTextBox.ScrollToCaret();

                // 创建关闭按钮
                var closeButton = new Button();
                closeButton.Text = "关闭";
                closeButton.Size = new System.Drawing.Size(80, 30);
                closeButton.Anchor = AnchorStyles.Bottom | AnchorStyles.Right;
                closeButton.Location = new System.Drawing.Point(form.ClientSize.Width - closeButton.Width - 10, form.ClientSize.Height - closeButton.Height - 10);
                closeButton.Click += (s, args) => form.Close();

                // 窗口大小变化时调整RichTextBox的边距，为关闭按钮留空间
                form.Resize += (s, args) =>
                {
                    richTextBox.Padding = new Padding(0, 0, 0, closeButton.Height + 20);
                };

                // 添加控件到窗体
                form.Controls.Add(richTextBox);
                form.Controls.Add(closeButton);

                // 显示对话框
                form.ShowDialog();
            }
        }

        /// <summary>
        /// 更新状态栏
        /// </summary>
        private void UpdateStatusBar(string message)
        {
            try
            {
                lblStatus.Text = message;
            }
            catch (Exception ex)
            {
                // 静默处理更新状态栏错误
            }
        }

        /// <summary>
        /// 显示测试报告对话框
        /// </summary>
        /// <param name="reportText">报告文本</param>
        /// <param name="title">对话框标题</param>
        private void ShowTestReportDialog(string reportText, string title)
        {
            var reportForm = new Form
            {
                Text = title,
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Both,
                Dock = DockStyle.Fill,
                Text = reportText,
                Font = new Font("Consolas", 9)
            };

            reportForm.Controls.Add(textBox);
            reportForm.ShowDialog(this);
        }

        /// <summary>
        /// 统一更新所有任务的最大offset限制（取任务周期和用户设定的较小值）
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="globalMaxOffsetUs">用户设定的全局最大offset限制(微秒)</param>
        private void UpdateTaskMaxOffsetLimits(List<TaskItem> tasks, int globalMaxOffsetUs)
        {
            try
            {
                // 获取时基值，用于计算最大offset
                int timeBaseUs = _appConfig.MainConfig.TimeBaseUs;
                
                foreach (var task in tasks)
                {
                    if (task.PeriodUs.HasValue && task.PeriodUs.Value > 0)
                    {
                        int taskPeriodUs = task.PeriodUs.Value;
                        
                        // 任务的最大offset = 周期 - 时基（确保offset是时基的整数倍且任务有时间执行）
                        int maxOffsetFromPeriod = Math.Max(0, taskPeriodUs - timeBaseUs);
                        
                        // 取(周期-时基)和用户设定的全局限制的较小值
                        task.MaxOffsetUs = Math.Min(maxOffsetFromPeriod, globalMaxOffsetUs);
                        
                        // 任务Offset限制已更新
                    }
                    else
                    {
                        // 如果任务周期无效，则使用全局限制
                        task.MaxOffsetUs = globalMaxOffsetUs;
                    }
                }
                
                // 完成所有任务的offset限制更新
            }
            catch (Exception ex)
            {
                // 静默处理更新任务offset限制错误
            }
        }

    }
}
