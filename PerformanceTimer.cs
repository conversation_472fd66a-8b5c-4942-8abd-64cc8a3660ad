using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;

namespace CalculateOffset
{
    /// <summary>
    /// 性能计时器工具类，用于统计和输出方法执行时间（支持多线程）
    /// </summary>
    public static class PerformanceTimer
    {
        // 线程本地的嵌套计时器支持，每个线程维护自己的栈
        private static readonly ThreadLocal<Stack<TimingInfo>> _timingStack = 
            new ThreadLocal<Stack<TimingInfo>>(() => new Stack<TimingInfo>());
        
        // 全局计时器存储（线程安全）
        private static readonly Dictionary<string, List<long>> _timingHistory = new Dictionary<string, List<long>>();
        
        // 用于保护_timingHistory的锁
        private static readonly object _historyLock = new object();
        
        /// <summary>
        /// 计时信息
        /// </summary>
        private class TimingInfo
        {
            public string Name { get; set; }
            public Stopwatch Stopwatch { get; set; }
            public int Depth { get; set; }
        }
        
        /// <summary>
        /// 开始计时
        /// </summary>
        /// <param name="operationName">操作名称</param>
        public static void Start(string operationName)
        {
            var stack = _timingStack.Value;
            var timing = new TimingInfo
            {
                Name = operationName,
                Stopwatch = Stopwatch.StartNew(),
                Depth = stack.Count
            };
            
            stack.Push(timing);
            
            // 输出开始信息（带缩进表示层级和线程ID）
            string indent = new string(' ', timing.Depth * 2);
            int threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
            //Console.WriteLine($"{indent}⏱️ [T{threadId}] 开始执行: {operationName}");
        }
        
        /// <summary>
        /// 结束计时并输出结果
        /// </summary>
        /// <param name="operationName">操作名称（可选，用于验证）</param>
        /// <returns>执行时间（毫秒）</returns>
        public static long Stop(string operationName = null)
        {
            var stack = _timingStack.Value;
            int threadId;

            if (stack.Count == 0)
            {
                threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
                //Console.WriteLine($"❌ [T{threadId}] 错误：没有正在进行的计时操作");
                return 0;
            }
            
            var timing = stack.Pop();
            timing.Stopwatch.Stop();
            
            // 验证操作名称（如果提供）
            threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
            if (!string.IsNullOrEmpty(operationName) && timing.Name != operationName)
            {
                //Console.WriteLine($"⚠️ [T{threadId}] 警告：计时操作名称不匹配。期望: {operationName}, 实际: {timing.Name}");
            }
            
            long elapsedMs = timing.Stopwatch.ElapsedMilliseconds;
            
            // 记录到历史记录（线程安全）
            lock (_historyLock)
            {
                if (!_timingHistory.ContainsKey(timing.Name))
                {
                    _timingHistory[timing.Name] = new List<long>();
                }
                _timingHistory[timing.Name].Add(elapsedMs);
            }
            
            // 输出结果（带缩进表示层级和线程ID）
            string indent = new string(' ', timing.Depth * 2);
            string timeDisplay = FormatTime(elapsedMs);
            //Console.WriteLine($"{indent}✅ [T{threadId}] 完成执行: {timing.Name} - 耗时: {timeDisplay}");
            
            return elapsedMs;
        }
        
        /// <summary>
        /// 格式化时间显示
        /// </summary>
        /// <param name="milliseconds">毫秒数</param>
        /// <returns>格式化的时间字符串</returns>
        private static string FormatTime(long milliseconds)
        {
            if (milliseconds < 1000)
            {
                return $"{milliseconds}ms";
            }
            else if (milliseconds < 60000)
            {
                return $"{milliseconds / 1000.0:F2}s";
            }
            else
            {
                var minutes = milliseconds / 60000;
                var seconds = (milliseconds % 60000) / 1000.0;
                return $"{minutes}m {seconds:F2}s";
            }
        }
        
        /// <summary>
        /// 输出性能统计摘要
        /// </summary>
        public static void PrintSummary()
        {
            lock (_historyLock)
            {
                if (_timingHistory.Count == 0)
                {
                    //Console.WriteLine("📊 性能统计摘要: 暂无计时数据");
                    return;
                }
                
                //Console.WriteLine("\n📊 ==================== 性能统计摘要 ====================");
                
                var sortedOperations = new List<(string name, double avgTime, int count, long totalTime)>();
                
                foreach (var kvp in _timingHistory)
                {
                    string operationName = kvp.Key;
                    var times = kvp.Value;
                    long totalTime = 0;
                    foreach (var time in times)
                    {
                        totalTime += time;
                    }
                    double avgTime = (double)totalTime / times.Count;
                    
                    sortedOperations.Add((operationName, avgTime, times.Count, totalTime));
                }
                
                // 按平均时间降序排列
                sortedOperations.Sort((a, b) => b.avgTime.CompareTo(a.avgTime));
                
                foreach (var operation in sortedOperations)
                {
                    string avgTimeStr = FormatTime((long)operation.avgTime);
                    string totalTimeStr = FormatTime(operation.totalTime);
                    
                    //Console.WriteLine($"🔹 {operation.name}:");
                    //Console.WriteLine($"   平均耗时: {avgTimeStr}");
                    //Console.WriteLine($"   调用次数: {operation.count}次");
                    //Console.WriteLine($"   总耗时: {totalTimeStr}");
                    
                    // 如果调用次数大于1，显示最小最大值
                    if (operation.count > 1)
                    {
                        var times = _timingHistory[operation.name];
                        long minTime = long.MaxValue;
                        long maxTime = long.MinValue;
                        foreach (var time in times)
                        {
                            if (time < minTime) minTime = time;
                            if (time > maxTime) maxTime = time;
                        }
                        //Console.WriteLine($"   耗时范围: {FormatTime(minTime)} ~ {FormatTime(maxTime)}");
                    }
                    //Console.WriteLine();
                }
                
                //Console.WriteLine("📊 ====================================================\n");
            }
        }
        
        /// <summary>
        /// 清除所有计时历史数据
        /// </summary>
        public static void Clear()
        {
            lock (_historyLock)
            {
                _timingHistory.Clear();
            }
            
            // 清除当前线程的计时栈
            var stack = _timingStack.Value;
            stack.Clear();
            
            //Console.WriteLine("🗑️ 已清除所有性能计时数据");
        }
        
        /// <summary>
        /// 使用using语法的计时器类
        /// </summary>
        public class TimingScope : IDisposable
        {
            private readonly string _operationName;
            private bool _disposed = false;
            
            public TimingScope(string operationName)
            {
                _operationName = operationName;
                Start(operationName);
            }
            
            public void Dispose()
            {
                if (!_disposed)
                {
                    Stop(_operationName);
                    _disposed = true;
                }
            }
        }
        
        /// <summary>
        /// 创建using语法的计时器
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>可用于using语句的计时器</returns>
        public static TimingScope CreateScope(string operationName)
        {
            return new TimingScope(operationName);
        }
    }
}
