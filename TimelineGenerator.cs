using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;

namespace CalculateOffset
{
    /// <summary>
    /// 时间轴网页生成器，用于生成任务调度的可视化时间轴
    /// </summary>
    public class TimelineGenerator
    {
        // TIMELINE_RESOLUTION 将根据用户输入的时基动态计算
        private const int MAX_TIMELINE_POINTS = 500000; // 最大时间点数限制
        
        /// <summary>
        /// 生成时间轴网页
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="timeBaseUs">时基值(微秒)</param>
        /// <param name="timeUnit">时基单位</param>
        /// <param name="validationResult">校验结果，为空时自行计算</param>
        /// <returns>生成是否成功</returns>
        public static bool GenerateTimelineHtml(List<TaskItem> tasks, int timeBaseUs, string timeUnit, ValidationResult validationResult = null, 
            int bestMaxOffsetUs = 0, int maxTasksCount = 0, string maxRatioStr = "", double calcVariance = 0, double bestVariance = 0, double overlapRate = 0.0)
        {
            try
            {
                // 过滤有效任务（有周期和Offset的任务）
                var validTasks = tasks.Where(t => 
                    t.PeriodUs.HasValue && t.PeriodUs.Value > 0 && 
                    t.OffsetUs.HasValue && t.OffsetUs.Value >= 0 &&
                    !string.IsNullOrWhiteSpace(t.TaskName)).ToList();
                
                if (validTasks.Count == 0)
                {
                    MessageBox.Show("没有有效的任务数据可生成时间轴", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }
                
                // 计算时间轴参数 - 使用超周期确保与校验报告一致
                // 获取超周期（已在会话中计算）
                int hyperPeriodUs = TaskUtility.CalculateHyperPeriod();
                double hyperPeriodMs = TaskUtility.ConvertToMilliseconds(hyperPeriodUs);
                
                // 计算任务的最大offset
                var tasksWithOffset = validTasks.Where(t => t.OffsetUs.HasValue);
                int maxOffsetUs = tasksWithOffset.Any() ? tasksWithOffset.Max(t => t.OffsetUs.Value) : 0;
                double maxOffsetMs = TaskUtility.ConvertToMilliseconds(maxOffsetUs);
                
                // 调整模拟时间长度：确保数据覆盖所有任务显示范围
                double simulationDurationMs = Math.Max(hyperPeriodMs * 2, hyperPeriodMs + maxOffsetMs);
                
                // 显示时间长度：确保能显示完整的超周期，同时适当显示有大offset的任务
                // 使用超周期的2倍，或者超周期加上一个任务周期的时间（取较大值）
                double maxTaskPeriodMs = validTasks.Max(t => TaskUtility.ConvertToMilliseconds(t.PeriodUs ?? 0));
                double calculatedDisplayDurationMs = Math.Max(hyperPeriodMs * 2, hyperPeriodMs + maxTaskPeriodMs);
                
                // 添加最大时间限制，防止显示时间过长
                const double MAX_DISPLAY_DURATION_MS = 2000.0; // 最大显示时间限制
                double displayDurationMs = Math.Min(calculatedDisplayDurationMs, MAX_DISPLAY_DURATION_MS);
                
                //Console.WriteLine($"[TimelineGenerator] 超周期: {hyperPeriodMs:F2}ms, 最大offset: {maxOffsetMs:F2}ms");
                //Console.WriteLine($"[TimelineGenerator] 计算显示时间: {calculatedDisplayDurationMs:F2}ms, 实际显示时间: {displayDurationMs:F2}ms");
                //if (calculatedDisplayDurationMs > MAX_DISPLAY_DURATION_MS)
                //{
                //    Console.WriteLine($"⚠️ [TimelineGenerator] 显示时间被截断到 {MAX_DISPLAY_DURATION_MS}ms，超过部分将不显示");
                //}
                
                // 生成任务颜色映射
                var taskColors = GenerateTaskColors(validTasks);
                
                // 计算抢占式时间轴数据，使用扩展的模拟时间长度
                int simulationDurationUs = TaskUtility.ConvertToMicroseconds(simulationDurationMs);
                var preemptiveData = CalculatePreemptiveTimelineData(validTasks, timeBaseUs, simulationDurationUs);
                
                // 计算统计数据，使用超周期长度确保统计准确性
                var statistics = validationResult != null ? 
                    CalculateStatisticsFromValidation(preemptiveData, hyperPeriodMs, validTasks, validationResult, timeBaseUs, timeUnit) :
                    CalculateStatistics(preemptiveData, hyperPeriodMs, validTasks, timeBaseUs, timeUnit);
                
                // 获取当前的jitter设置值（所有任务应该有相同的jitter值）
                int jitterTimeUs = validTasks.Count > 0 ? validTasks[0].JitterTimeUs : 0;
                
                // 生成HTML内容 - 传递显示时间长度和超周期长度
                string htmlContent = GenerateHtmlContent(validTasks, taskColors, preemptiveData, statistics, 
                    displayDurationMs, hyperPeriodMs, timeBaseUs, timeUnit,
                    bestMaxOffsetUs, maxTasksCount, maxRatioStr, calcVariance, bestVariance, overlapRate, jitterTimeUs);
                
                // 保存HTML文件
                string htmlFilePath = GetHtmlFilePath();
                File.WriteAllText(htmlFilePath, htmlContent, Encoding.UTF8);
                
                // 询问用户是否打开网页
                var result = MessageBox.Show($"时间轴网页已生成：\n{htmlFilePath}\n\n是否立即打开网页？", 
                    "时间轴生成成功", MessageBoxButtons.YesNo, MessageBoxIcon.Information);
                
                if (result == DialogResult.Yes)
                {
                    try
                    {
                        System.Diagnostics.Process.Start(htmlFilePath);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"打开网页失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"生成时间轴失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// 根据用户输入的时基和单位计算时间轴分辨率（已废弃，保留用于兼容性）
        /// </summary>
        private static double CalculateTimelineResolution(int timeBaseUs, string timeUnit)
        {
            // 将时基转换为毫秒
            double timeBaseInMs = TaskUtility.ConvertToMilliseconds(timeBaseUs);
            
            // 返回每毫秒的采样点数（分辨率）
            return 1.0 / timeBaseInMs;
        }
        
        /// <summary>
        /// 生成任务颜色映射
        /// </summary>
        private static Dictionary<string, string> GenerateTaskColors(List<TaskItem> tasks)
        {
            var colors = new Dictionary<string, string>();
            
            // 预定义的颜色列表，确保颜色差异明显，避免相近色彩
            string[] colorPalette = {
                "#E74C3C", "#3498DB", "#2ECC71", "#F39C12", "#9B59B6",
                "#1ABC9C", "#E67E22", "#34495E", "#F1C40F", "#E91E63",
                "#8E44AD", "#16A085", "#D35400", "#2980B9", "#27AE60",
                "#C0392B", "#7F8C8D", "#F39800", "#8BC34A", "#FF5722",
                "#607D8B", "#795548", "#FF9800", "#4CAF50", "#2196F3",
                "#9C27B0", "#009688", "#FF5252", "#536DFE", "#4FC3F7"
            };
            
            for (int i = 0; i < tasks.Count; i++)
            {
                string taskName = tasks[i].TaskName;
                if (!string.IsNullOrWhiteSpace(taskName))
                {
                    colors[taskName] = colorPalette[i % colorPalette.Length];
                }
            }
            
            return colors;
        }
        
        /// <summary>
        /// 计算抢占式时间轴数据（复用OffsetValidator的模拟结果，消除代码冗余）
        /// </summary>
        /// <param name="validTasks">有效任务列表</param>
        /// <param name="timeBaseUs">时基（微秒）</param>
        /// <param name="timelineDurationUs">时间轴长度（微秒）</param>
        private static PreemptiveTimelineData CalculatePreemptiveTimelineData(List<TaskItem> validTasks, int timeBaseUs, int timelineDurationUs)
        {
            // 创建OffsetValidator来进行抢占式调度模拟
            var validator = new OffsetValidator(timeBaseUs);
            
            // 使用调整后的时间轴长度进行模拟，确保数据覆盖整个显示范围
            var validationResult = validator.ValidateOffsets(validTasks, timelineDurationUs);
            
            // 复用OffsetValidator的详细时间线数据，避免重复的抢占式调度模拟
            var detailedData = validator.GetDetailedTimelineData();
            
            // 创建扩展的时间线数据，包含ValidationResult
            var preemptiveData = new PreemptiveTimelineData
            {
                ValidationResult = validationResult
            };
            
            // 从详细数据复制属性
            preemptiveData.DistributionData = detailedData.DistributionData;
            preemptiveData.TaskSegments = detailedData.TaskSegments;
            preemptiveData.HyperPeriodUs = detailedData.HyperPeriodUs;
            preemptiveData.TimeBaseUs = detailedData.TimeBaseUs;
            
            return preemptiveData;
        }
        

        

        
        /// <summary>
        /// 计算统计数据
        /// </summary>
        private static TimelineStatistics CalculateStatistics(PreemptiveTimelineData preemptiveData, double durationMs, List<TaskItem> tasks, int timeBaseUs, string timeUnit)
        {
            if (preemptiveData.DistributionData.Count == 0)
                return new TimelineStatistics();
                
            // 统计抢占级别分布
            var preemptionLevelCounts = preemptiveData.DistributionData
                .GroupBy(tp => tp.PreemptionLevel == 0 ? 0 : 1) // 0=空闲，1=有任务执行
                .ToDictionary(g => g.Key, g => g.Count());
            
            // 计算空闲率
            double idlePercentage = preemptionLevelCounts.ContainsKey(0) ? 
                (double)preemptionLevelCounts[0] / preemptiveData.DistributionData.Count * 100 : 0;
            
            // 直接使用ValidationResult的CPU负载率
            double cpuLoadPercentage = preemptiveData.ValidationResult?.CpuUsage ?? 0;
            
            // 统计任务执行时间
            var taskExecutionCounts = preemptiveData.DistributionData
                .Where(tp => !string.IsNullOrEmpty(tp.ExecutingTask))
                .GroupBy(tp => tp.ExecutingTask)
                .ToDictionary(g => g.Key, g => g.Count());
            
            return new TimelineStatistics
            {
                MinTaskCount = 0,
                MaxTaskCount = 1, // 抢占式调度下任意时刻最多只有一个任务执行
                MinTimePointsUs = new List<int>(),
                MaxTimePointsUs = new List<int>(),
                AverageTaskCount = cpuLoadPercentage / 100.0, // 平均负载率即为平均任务数
                DurationMs = durationMs,
                PreemptionLevelDistribution = preemptionLevelCounts,
                TaskExecutionCounts = taskExecutionCounts,
                CpuLoadPercentage = cpuLoadPercentage,
                PeakLoadPercentage = 100 - idlePercentage,
                IdlePercentage = idlePercentage
            };
        }
        

        
        /// <summary>
        /// 基于ValidationResult计算统计数据，确保数据一致性
        /// </summary>
        private static TimelineStatistics CalculateStatisticsFromValidation(PreemptiveTimelineData preemptiveData, double durationMs, List<TaskItem> tasks, ValidationResult validationResult, int timeBaseUs, string timeUnit)
        {
            if (preemptiveData.DistributionData.Count == 0)
                return new TimelineStatistics();
                
            // 统计抢占级别分布
            var preemptionLevelCounts = preemptiveData.DistributionData
                .GroupBy(tp => tp.PreemptionLevel == 0 ? 0 : 1) // 0=空闲，1=有任务执行
                .ToDictionary(g => g.Key, g => g.Count());
            
            // 计算空闲率
            double idlePercentage = preemptionLevelCounts.ContainsKey(0) ? 
                (double)preemptionLevelCounts[0] / preemptiveData.DistributionData.Count * 100 : 0;
            
            // 直接使用ValidationResult中的CPU负载率，确保完全一致
            double cpuLoadPercentage = validationResult.CpuUsage;
            
            // 统计任务执行时间
            var taskExecutionCounts = preemptiveData.DistributionData
                .Where(tp => !string.IsNullOrEmpty(tp.ExecutingTask))
                .GroupBy(tp => tp.ExecutingTask)
                .ToDictionary(g => g.Key, g => g.Count());
            
            return new TimelineStatistics
            {
                MinTaskCount = 0,
                MaxTaskCount = 1, // 抢占式调度下任意时刻最多只有一个任务执行
                MinTimePointsUs = new List<int>(),
                MaxTimePointsUs = new List<int>(),
                AverageTaskCount = cpuLoadPercentage / 100.0, // 平均负载率即为平均任务数
                DurationMs = durationMs,
                PreemptionLevelDistribution = preemptionLevelCounts,
                TaskExecutionCounts = taskExecutionCounts,
                CpuLoadPercentage = cpuLoadPercentage,
                PeakLoadPercentage = 100 - idlePercentage,
                IdlePercentage = idlePercentage,
                ValidationResult = validationResult // 保存校验结果供HTML使用
            };
        }
        
        /// <summary>
        /// 生成HTML内容
        /// </summary>
        private static string GenerateHtmlContent(List<TaskItem> tasks, Dictionary<string, string> taskColors, 
            PreemptiveTimelineData preemptiveData, TimelineStatistics statistics, double timelineDurationMs, double hyperPeriodMs, int timeBaseUs, string timeUnit,
            int bestMaxOffsetUs, int maxTasksCount, string maxRatioStr, double calcVariance, double bestVariance, double overlapRate, int jitterTimeUs)
        {
            var html = new StringBuilder();
            
            // 动态计算Canvas宽度，确保任务块有足够的显示空间
            // 基础比例：每毫秒40像素，确保5ms任务有200px宽度
            const double PIXELS_PER_MS = 40.0;
            const int MIN_CANVAS_WIDTH = 2000;  // 最小宽度
            const int MAX_CANVAS_WIDTH = 40000; // 最大宽度，防止浏览器性能问题
            
            int dynamicCanvasWidth = (int)Math.Round(timelineDurationMs * PIXELS_PER_MS);
            dynamicCanvasWidth = Math.Max(MIN_CANVAS_WIDTH, Math.Min(MAX_CANVAS_WIDTH, dynamicCanvasWidth));
            
            //Console.WriteLine($"[TimelineGenerator] 动态Canvas宽度计算: {timelineDurationMs:F2}ms * {PIXELS_PER_MS} = {dynamicCanvasWidth}px");
            
            // 计算任务块最小宽度（3像素，确保极短任务可见）
            const int MIN_TASK_WIDTH_PIXELS = 3; // 最小3像素宽度
            double minTaskWidthMs = MIN_TASK_WIDTH_PIXELS / PIXELS_PER_MS; // 3像素对应的毫秒数
            //Console.WriteLine($"[TimelineGenerator] 任务块最小宽度: {minTaskWidthMs:F3}ms ({MIN_TASK_WIDTH_PIXELS}px)");
            
            html.AppendLine("<!DOCTYPE html>");
            html.AppendLine("<html lang='zh-CN'>");
            html.AppendLine("<head>");
            html.AppendLine("    <meta charset='UTF-8'>");
            html.AppendLine("    <meta name='viewport' content='width=device-width, initial-scale=1.0'>");
            html.AppendLine("    <title>任务调度时间轴</title>");
            html.AppendLine("    <style>");
            html.AppendLine(GenerateCss());
            html.AppendLine("    </style>");
            html.AppendLine("</head>");
            html.AppendLine("<body>");
            
                         // 页面标题
             html.AppendLine("    <div class='header'>");
             html.AppendLine("        <h1>任务调度时间轴分析</h1>");
                           var maxPeriodMs = tasks.Max(t => TaskUtility.ConvertToMilliseconds(t.PeriodUs ?? 0));
              var maxOffsetMs = tasks.Max(t => TaskUtility.ConvertToMilliseconds(t.OffsetUs ?? 0));
             html.AppendLine($"        <p>生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss} | 时基: {timeBaseUs}{timeUnit} | 超周期: {hyperPeriodMs:F0}ms</p>");
             html.AppendLine($"        <p>最大周期: {maxPeriodMs:F0}ms | 最大偏移: {maxOffsetMs:F2}ms | 时间轴长度: {timelineDurationMs:F0}ms | 任务总数: {tasks.Count}</p>");
             html.AppendLine("    </div>");
            
                         // 统计数据
             html.AppendLine("    <div class='statistics'>");
             html.AppendLine("        <h2>统计数据</h2>");
             html.AppendLine("        <div class='stats-grid'>");
             
             // 显示关键调度分析数据，分两行：
             // 第一行：时基值，推荐最大Offset，CPU负载率(估算)，当前任务方差，最佳任务方差
             // 第二行：抢占数，抢占率，可调度性，系统成本
             
             // === 第一行：保留原有核心参数 ===
             // 1. 时基值
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='系统使用的时基值，用于任务调度的基础时间单位'>时基值:</span>");
             html.AppendLine($"                <span class='stat-value'>{timeBaseUs}{timeUnit}</span>");
             html.AppendLine($"            </div>");
             
             // 2. 推荐最大Offset
             if (bestMaxOffsetUs > 0)
             {
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='推荐的最大偏移值设定，用于优化任务调度'>推荐最大Offset:</span>");
                 html.AppendLine($"                <span class='stat-value'>{TaskUtility.ConvertToMilliseconds(bestMaxOffsetUs):F1}ms</span>");
                 html.AppendLine($"            </div>");
             }
             
              // 3. 抖动值（支持每任务不同jitter：同值则显示单值；否则显示范围与均值）
              var jitterListUs = tasks.Select(t => t.JitterTimeUs).ToList();
              bool allSameJitter = jitterListUs.Count > 0 && jitterListUs.All(v => v == jitterListUs[0]);
              if (allSameJitter)
              {
                  double jitterMsSingle = TaskUtility.ConvertToMilliseconds(jitterListUs[0]);
                  html.AppendLine($"            <div class='stat-item'>");
                  html.AppendLine($"                <span class='stat-label' title='任务切换时操作系统的开销时间，用于更准确地评估系统负载'>抖动值:</span>");
                  html.AppendLine($"                <span class='stat-value'>{jitterMsSingle:F3}ms</span>");
                  html.AppendLine($"            </div>");
              }
              else
              {
                  int minJitterUs = jitterListUs.Min();
                  int maxJitterUs = jitterListUs.Max();
                  double avgJitterMs = jitterListUs.Average(v => TaskUtility.ConvertToMilliseconds(v));
                  html.AppendLine($"            <div class='stat-item'>");
                  html.AppendLine($"                <span class='stat-label' title='不同任务的抖动值不一致，显示范围与均值'>抖动(范围/均值):</span>");
                  html.AppendLine($"                <span class='stat-value'>{TaskUtility.ConvertToMilliseconds(minJitterUs):F3}~{TaskUtility.ConvertToMilliseconds(maxJitterUs):F3}ms / {avgJitterMs:F3}ms</span>");
                  html.AppendLine($"            </div>");
              }
             
             // 4. CPU负载率(估算)
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='CPU的理论负载率(不含中断)，基于所有任务的执行时间与周期的比值计算，表示CPU的利用率'>CPU负载率(估算):</span>");
             html.AppendLine($"                <span class='stat-value'>{statistics.CpuLoadPercentage:F1}%</span>");
             html.AppendLine($"            </div>");
             
             // 5. 当前任务方差
             if (calcVariance > 0)
             {
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='当前计算得出的任务方差值，反映任务分布的均匀程度'>当前任务方差:</span>");
                 html.AppendLine($"                <span class='stat-value'>{calcVariance:F4}</span>");
                 html.AppendLine($"            </div>");
             }
             
             // 6. 最佳任务方差
             if (bestVariance > 0)
             {
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='理论上的最佳任务方差值，用于比较当前配置的优化程度'>最佳任务方差:</span>");
                 html.AppendLine($"                <span class='stat-value'>{bestVariance:F4}</span>");
                 html.AppendLine($"            </div>");
             }
             
             // === 第二行：新增抢占和调度分析数据 ===
             // 从ValidationResult获取调度分析数据
             ValidationResult validationResult = statistics.ValidationResult ?? preemptiveData.ValidationResult;
             
             if (validationResult != null)
             {
                 // 7. 抢占数
                 int preemptionCount = validationResult.PreemptionEvents?.Count ?? 0;
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='在超周期内发生的任务抢占事件总数。抢占发生在高优先级任务打断低优先级任务执行时，体现了实时调度系统的动态特性'>抢占数:</span>");
                 html.AppendLine($"                <span class='stat-value'>{preemptionCount}</span>");
                 html.AppendLine($"            </div>");
                 
                 // 8. 抢占率
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='发生抢占的任务实例占总任务实例的百分比。较高的抢占率表明系统任务间优先级冲突较多，可能需要优化任务配置'>抢占率:</span>");
                 html.AppendLine($"                <span class='stat-value'>{validationResult.PreemptionRatio:F2}%</span>");
                 html.AppendLine($"            </div>");
                 
                 // 9. 启动重叠率（静态） - 直接复用挑选阶段缓存
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='静态分析：超周期内所有任务的启动时刻是否落入其他任务静态执行窗口的比例（不考虑抢占），越小越好'>启动重叠率:</span>");
                double computedStartOverlapRate = 0.0;
                try { computedStartOverlapRate = validationResult?.ExtraMetrics?.StartOverlapRate ?? 0.0; } catch {}
                html.AppendLine($"                <span class='stat-value'>{computedStartOverlapRate:F3}</span>");
                 html.AppendLine($"            </div>");

                // 10. 动态忙比例 - 直接复用挑选阶段缓存
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='动态抢占分析：超周期内所有任务启动时刻CPU是否被其他任务占用的比例（明确排除自身执行），越小越好'>动态忙比例:</span>");
                double computedDynamicBusy = 0.0;
                try { computedDynamicBusy = validationResult?.ExtraMetrics?.DynamicBusyAtStartRatio ?? 0.0; } catch {}
                  html.AppendLine($"                <span class='stat-value'>{computedDynamicBusy:F3}</span>");
                 html.AppendLine($"            </div>");

                 // 11. 静态重叠率（综合）
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='静态重叠率综合指标：考虑重叠深度与均衡性（加权重叠率×(2-均衡性)），越小越好'>静态重叠率:</span>");
                 html.AppendLine($"                <span class='stat-value'>{overlapRate:F3}</span>");
                 html.AppendLine($"            </div>");
                 
                 // 10. 可调度性
                 string schedulabilityText = validationResult.IsSchedulable ? "可调度" : "不可调度";
                 string schedulabilityClass = validationResult.IsSchedulable ? "stat-value" : "stat-value-error";
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='系统是否满足实时调度要求。可调度表示所有任务都能在截止时间内完成，不可调度表示存在任务无法满足时间约束'>可调度性:</span>");
                 html.AppendLine($"                <span class='{schedulabilityClass}'>{schedulabilityText}</span>");
                 html.AppendLine($"            </div>");
                 
                 // 10. 系统成本
                 string costClass = validationResult.CostValue <= 1.0 ? "stat-value" : "stat-value-warning";
                 html.AppendLine($"            <div class='stat-item'>");
                 html.AppendLine($"                <span class='stat-label' title='系统调度成本综合评价指标。成本≤1.0表示系统运行良好，成本>1.0表示存在调度风险，值越高风险越大'>系统成本:</span>");
                 html.AppendLine($"                <span class='{costClass}'>{validationResult.CostValue:F3}</span>");
                 html.AppendLine($"            </div>");
             }
             
             // 添加采样和时间相关参数
             var totalPoints = preemptiveData.DistributionData.Count;
             
             // 11. 总采样点数
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='时间线分析中的总采样点数，反映分析的精度和时间轴的分辨率'>总采样点数:</span>");
             html.AppendLine($"                <span class='stat-value'>{totalPoints:N0}</span>");
             html.AppendLine($"            </div>");
             
             // 12. 采样精度
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='时间线采样的精度，表示每个采样点代表的时间间隔'>采样精度:</span>");
             html.AppendLine($"                <span class='stat-value'>{TaskUtility.ConvertToMilliseconds(preemptiveData.TimeBaseUs):F3}ms</span>");
             html.AppendLine($"            </div>");
             
             // 13. 超周期时长
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='所有任务周期的最小公倍数，系统调度的完整周期时长'>超周期时长:</span>");
             html.AppendLine($"                <span class='stat-value'>{hyperPeriodMs:F2}ms</span>");
             html.AppendLine($"            </div>");
             
             // 14. 时间轴长度
             html.AppendLine($"            <div class='stat-item'>");
             html.AppendLine($"                <span class='stat-label' title='时间轴显示的总长度，为确保任务完整显示而调整，取2倍超周期和(超周期+最大offset)的最大值'>时间轴长度:</span>");
             html.AppendLine($"                <span class='stat-value'>{timelineDurationMs:F2}ms</span>");
             html.AppendLine($"            </div>");
             
             html.AppendLine("        </div>");
             html.AppendLine("    </div>");
            
            // 任务列表
            html.AppendLine("    <div class='task-legend'>");
            html.AppendLine("        <h2>任务列表</h2>");
            html.AppendLine("        <div class='legend-grid'>");
            foreach (var task in tasks)
            {
                if (!string.IsNullOrWhiteSpace(task.TaskName) && taskColors.ContainsKey(task.TaskName))
                {
                    html.AppendLine($"            <div class='legend-item'>");
                    html.AppendLine($"                <div class='color-box' style='background-color: {taskColors[task.TaskName]}'></div>");
                    html.AppendLine($"                <span class='task-info'>");
                                         html.AppendLine($"                    <strong>{task.TaskName}</strong><br>");
                     html.AppendLine($"                    周期: {TaskUtility.ConvertToMilliseconds(task.PeriodUs ?? 0):F0}ms | Offset: {TaskUtility.ConvertToMilliseconds(task.OffsetUs ?? 0):F2}ms");
                     if (task.ExecutionTimeUs.HasValue && task.ExecutionTimeUs.Value > 0)
                     {
                         html.AppendLine($"<br>                    执行时间: {TaskUtility.ConvertToMilliseconds(task.ExecutionTimeUs.Value):F2}ms");
                     }
                     else
                     {
                         html.AppendLine($"<br>                    执行时间: 0.10ms (默认100us)");
                     }
                     if (task.Priority.HasValue)
                     {
                         html.AppendLine($" | 优先级: {task.Priority}");
                     }
                    html.AppendLine($"                </span>");
                    html.AppendLine($"            </div>");
                }
            }
            html.AppendLine("        </div>");
            html.AppendLine("    </div>");
            
            // 抢占式调度时间轴
             html.AppendLine("    <div class='timeline-container'>");
             html.AppendLine("        <h2>抢占式调度时间轴</h2>");
             html.AppendLine("        <div class='timeline-wrapper'>");
              html.AppendLine($"            <canvas id='distributionCanvas' width='{dynamicCanvasWidth}' height='120'></canvas>");
             html.AppendLine("            <div class='time-labels' id='distributionLabels'></div>");
             html.AppendLine("        </div>");
             html.AppendLine("    </div>");
             
             // 分布式任务排布时间轴
             html.AppendLine("    <div class='timeline-container'>");
             html.AppendLine("        <h2>分布式排布时间轴</h2>");
             html.AppendLine("        <div class='timeline-wrapper'>");
              html.AppendLine($"            <canvas id='taskCanvas' width='{dynamicCanvasWidth}' height='200'></canvas>");
             html.AppendLine("            <div class='time-labels' id='taskLabels'></div>");
             html.AppendLine("        </div>");
             html.AppendLine("    </div>");
            
                         html.AppendLine("    <script>");
             html.AppendLine(GenerateJavaScript(preemptiveData, taskColors, timelineDurationMs, tasks, statistics, dynamicCanvasWidth, minTaskWidthMs, timeBaseUs));
             html.AppendLine("    </script>");
            html.AppendLine("</body>");
            html.AppendLine("</html>");
            
            return html.ToString();
        }
        
        /// <summary>
        /// 生成CSS样式
        /// </summary>
        private static string GenerateCss()
        {
            return @"
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'SimSun', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .statistics {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .statistics h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        
        .stat-label {
            font-weight: bold;
            color: #2c3e50;
            cursor: help;
            transition: color 0.1s ease;
            position: relative;
        }
        
        .stat-label:hover {
            color: #3498db;
            text-decoration: underline;
        }
        
        /* 立即显示tooltip，智能定位避免超出屏幕 */
        .stat-label[title]:hover::after {
            content: attr(title);
            position: fixed;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 6px;
            font-size: 13px;
            z-index: 10000;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            pointer-events: none;
            min-width: 200px;
            max-width: 400px;
            width: max-content;
            white-space: normal;
            text-align: left;
            line-height: 1.5;
            /* 默认居中定位 */
            bottom: calc(100vh - var(--mouse-y, 0px) + 10px);
            left: var(--mouse-x, 0px);
            transform: translateX(-50%);
        }
        
        /* 左对齐tooltip */
        .stat-label.tooltip-left[title]:hover::after {
            transform: translateX(0);
            left: var(--mouse-x, 0px);
        }
        
        /* 右对齐tooltip */
        .stat-label.tooltip-right[title]:hover::after {
            transform: translateX(-100%);
            left: var(--mouse-x, 0px);
        }
        
        /* 居中tooltip（默认） */
        .stat-label.tooltip-center[title]:hover::after {
            transform: translateX(-50%);
            left: var(--mouse-x, 0px);
        }
        
        /* 默认tooltip箭头 */
        .stat-label[title]:hover::before {
            content: '';
            position: fixed;
            border: 5px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.9);
            z-index: 10001;
            bottom: calc(100vh - var(--mouse-y, 0px) + 5px);
            left: var(--mouse-x, 0px);
            transform: translateX(-50%);
        }
        
        /* 左对齐tooltip的箭头 */
        .stat-label.tooltip-left[title]:hover::before {
            transform: translateX(0);
            left: calc(var(--mouse-x, 0px) + 20px);
        }
        
        /* 右对齐tooltip的箭头 */
        .stat-label.tooltip-right[title]:hover::before {
            transform: translateX(-100%);
            left: calc(var(--mouse-x, 0px) - 20px);
        }
        
        /* 居中tooltip的箭头 */
        .stat-label.tooltip-center[title]:hover::before {
            transform: translateX(-50%);
            left: var(--mouse-x, 0px);
        }
        
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #e74c3c;
        }
        
        .stat-value-error {
            font-size: 18px;
            font-weight: bold;
            color: #c0392b;
            background: #fdf2f2;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .stat-value-warning {
            font-size: 18px;
            font-weight: bold;
            color: #f39c12;
            background: #fef9e7;
            padding: 2px 8px;
            border-radius: 4px;
        }
        
        .task-legend {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .task-legend h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
        .legend-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 10px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        
        .color-box {
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .task-info {
            font-size: 12px;
            line-height: 1.4;
        }
        
        .timeline-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .timeline-container h2 {
            color: #2c3e50;
            margin-bottom: 15px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 5px;
        }
        
                 .timeline-wrapper {
             position: relative;
             overflow-x: auto;
             border: 1px solid #ddd;
             border-radius: 5px;
             max-width: 100%;
             width: 100%;
         }
         
                 #distributionCanvas, #taskCanvas {
            display: block;
            cursor: crosshair;
            min-width: 2000px; /* 初始最小宽度，将通过JavaScript动态调整 */
        }
        
        .time-labels {
            height: 30px;
            position: relative;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
            min-width: 2000px; /* 初始最小宽度，将通过JavaScript动态调整 */
            width: 100%;
        }
        
        .time-label {
            position: absolute;
            top: 5px;
            font-size: 10px;
            color: #666;
            /* transform由JavaScript动态设置，避免0ms标签被遮挡 */
        }
        
        .tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 10000;
            max-width: 250px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            white-space: nowrap;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        
";
        }
        
                 /// <summary>
         /// 生成JavaScript代码
         /// </summary>
         private static string GenerateJavaScript(PreemptiveTimelineData preemptiveData, Dictionary<string, string> taskColors, double durationMs, List<TaskItem> tasks, TimelineStatistics statistics, int canvasWidth, double minTaskWidthMs, int timeBaseUs)
         {
             var js = new StringBuilder();
             
             js.AppendLine("        const distributionCanvas = document.getElementById('distributionCanvas');");
            js.AppendLine("        const distributionCtx = distributionCanvas.getContext('2d');");
            js.AppendLine("        const distributionLabels = document.getElementById('distributionLabels');");
            js.AppendLine("        const taskCanvas = document.getElementById('taskCanvas');");
            js.AppendLine($"        const canvasWidth = {canvasWidth}; // 动态Canvas宽度");
            js.AppendLine($"        const minTaskWidthMs = {minTaskWidthMs:F3}; // 任务块最小显示宽度(ms)");
            js.AppendLine($"        const timeBaseMs = {TaskUtility.ConvertToMilliseconds(timeBaseUs):F3}; // 时基(ms)");
             // 固定的任务色块高度（为原60px的2/3=40px），两个画布统一使用
             js.AppendLine("        const FIXED_BLOCK_HEIGHT = 40; // 任务色块固定高度");
             js.AppendLine("        const LAYER_GAP = 2;      // 层级内上下留白");
             js.AppendLine("        const ROW_GAP = 0;        // 任务行之间的间距");
             js.AppendLine("        const TOP_MARGIN = 10;    // 上边距");
             js.AppendLine("        const BOTTOM_MARGIN = 0; // 下边距");
            js.AppendLine("");
            js.AppendLine("        const taskCtx = taskCanvas.getContext('2d');");
            js.AppendLine("        const taskLabels = document.getElementById('taskLabels');");
            js.AppendLine("");
            js.AppendLine("        // 动态设置Canvas和time-labels的最小宽度");
            js.AppendLine($"        distributionCanvas.style.minWidth = '{canvasWidth}px';");
            js.AppendLine($"        taskCanvas.style.minWidth = '{canvasWidth}px';");
            js.AppendLine($"        distributionLabels.style.minWidth = '{canvasWidth}px';");
            js.AppendLine($"        taskLabels.style.minWidth = '{canvasWidth}px';");
            js.AppendLine("");
             
                         // 抢占式时间轴数据 - 过滤到显示时间范围内，并适度采样以平衡性能和精度
            js.AppendLine("        const preemptiveData = [");
            
            // 首先过滤出显示时间范围内的数据点
            var filteredData = preemptiveData.DistributionData
                .Where(p => TaskUtility.ConvertToMilliseconds(p.TimeUs) <= durationMs)
                .ToList();
            
            // 计算采样步长：如果数据点过多，进行适度采样，但保持足够精度
            int sampleStep = Math.Max(1, filteredData.Count / 2000); // 最多2000个采样点
            var sampledData = filteredData.Where((p, i) => i % sampleStep == 0).ToList();
            foreach (var point in sampledData)
            {
                double timeMs = TaskUtility.ConvertToMilliseconds(point.TimeUs);
                string executingTask = string.IsNullOrEmpty(point.ExecutingTask) ? "" : EscapeJavaScriptString(point.ExecutingTask);
                int preemptionLevel = string.IsNullOrEmpty(point.ExecutingTask) ? 0 : point.PreemptionLevel;
                js.AppendLine($"            {{timeMs: {timeMs.ToString("F3", System.Globalization.CultureInfo.InvariantCulture)}, executingTask: '{executingTask}', preemptionLevel: {preemptionLevel}}},");
            }
             js.AppendLine("        ];");
             js.AppendLine("");
             
             // 任务颜色映射
             js.AppendLine("        const taskColors = {");
             foreach (var kvp in taskColors)
             {
                 string escapedTaskName = EscapeJavaScriptString(kvp.Key);
                 js.AppendLine($"            '{escapedTaskName}': '{kvp.Value}',");
             }
             js.AppendLine("        };");
             js.AppendLine("");
             
             // 任务段数据
             js.AppendLine("        const taskSegments = {");
             foreach (var taskSegmentPair in preemptiveData.TaskSegments)
             {
                 string escapedTaskName = EscapeJavaScriptString(taskSegmentPair.Key);
                 js.AppendLine($"            '{escapedTaskName}': [");
                 
                
                // 过滤出显示时间范围内的段
                var filteredSegments = taskSegmentPair.Value
                    .Where(segment => 
                    {
                        double startMs = TaskUtility.ConvertToMilliseconds(segment.StartTimeUs);
                        double endMs = TaskUtility.ConvertToMilliseconds(segment.EndTimeUs);
                        // 段的开始时间在显示范围内，或者段跨越显示范围
                        return startMs < durationMs && endMs > 0;
                    })
                    .ToList();
                
                foreach (var segment in filteredSegments)
                {
                    double startMs = TaskUtility.ConvertToMilliseconds(segment.StartTimeUs);
                    double endMs = TaskUtility.ConvertToMilliseconds(segment.EndTimeUs);
                    int preemptionLevel = segment.PreemptionLevel;
                    double jitterMs = TaskUtility.ConvertToMilliseconds(segment.JitterTimeUs);
                    
                    // 裁剪段的时间范围到显示范围内
                    double clippedStartMs = Math.Max(0, startMs);
                    double clippedEndMs = Math.Min(durationMs, endMs);
                    
                    // 调试输出C#端的段信息
                    //Console.WriteLine($"   {clippedStartMs:F2}-{clippedEndMs:F2}ms, 层级={preemptionLevel}, jitter={jitterMs:F3}ms (原始: {startMs:F2}-{endMs:F2}ms)");
                    
                    js.AppendLine($"                {{startMs: {clippedStartMs.ToString("F3", System.Globalization.CultureInfo.InvariantCulture)}, endMs: {clippedEndMs.ToString("F3", System.Globalization.CultureInfo.InvariantCulture)}, preemptionLevel: {preemptionLevel}, jitterMs: {jitterMs.ToString("F3", System.Globalization.CultureInfo.InvariantCulture)}}},");
                }
                 js.AppendLine($"            ],");
             }
             js.AppendLine("        };");
             js.AppendLine("");
             
             // 原始任务数据
             js.AppendLine("        const originalTasks = {");
             foreach (var task in tasks)
             {
                 if (!string.IsNullOrWhiteSpace(task.TaskName))
                 {
                     string escapedTaskName = EscapeJavaScriptString(task.TaskName);
                     js.AppendLine($"            '{escapedTaskName}': {{");
                     js.AppendLine($"                periodMs: {TaskUtility.ConvertToMilliseconds(task.PeriodUs ?? 0)},");
                     js.AppendLine($"                offsetMs: {TaskUtility.ConvertToMilliseconds(task.OffsetUs ?? 0)},");
                     js.AppendLine($"                executionTimeMs: {TaskUtility.ConvertToMilliseconds(task.ExecutionTimeUs ?? 100)},");
                     js.AppendLine($"                jitterMs: {TaskUtility.ConvertToMilliseconds(task.JitterTimeUs)}");
                     js.AppendLine($"            }},");
                 }
             }
             js.AppendLine("        };");
             js.AppendLine("");
             
             js.AppendLine($"        const durationMs = {durationMs.ToString("F3", System.Globalization.CultureInfo.InvariantCulture)};");
             js.AppendLine("");
             
             // 添加统一的时间-坐标转换函数，确保完美对齐
             js.AppendLine(@"
         // 统一的时间-坐标转换函数，确保时间标签和任务段完美对齐
         function timeToPixel(timeMs, durationMs, canvasWidth) {
             return (timeMs / durationMs) * canvasWidth;
         }
         
         function pixelToTime(pixelX, durationMs, canvasWidth) {
             return (pixelX / canvasWidth) * durationMs;
         }
         
         // 智能计算时间标尺间隔
         function calculateTimeInterval(totalDurationMs, timeBaseMs) {
             // 目标：在最大情况下每5ms一个刻度，根据时间跨度动态适应
             const targetMaxIntervalMs = 5.0; // 最大间隔5ms
             const minIntervalMs = Math.max(0.1, timeBaseMs / 2); // 最小间隔：时基的一半或0.1ms
             
             // 候选间隔序列：优先选择整数和易读的分数
             const candidateIntervals = [
                 0.1, 0.2, 0.5, 1, 2, 2.5, 5, 10, 20, 25, 50, 100, 200, 250, 500
             ];
             
             // 动态计算合适的标签数量范围，长时间轴允许更多标签
             const minLabels = 8;  // 最少8个标签
             const baseMaxLabels = 25; // 基础最大标签数
             // 为5ms间隔预留足够空间：Math.floor(totalDurationMs/5) + 1 + 安全边距
             const dynamicMaxLabels = Math.min(500, Math.max(baseMaxLabels, Math.floor(totalDurationMs / 5) + 10)); // 长时间轴动态增加上限
             
             // 优先尝试目标间隔5ms，即使标签数量较多
             if (targetMaxIntervalMs >= minIntervalMs) {
                 const targetLabelCount = Math.floor(totalDurationMs / targetMaxIntervalMs) + 1;
                 if (targetLabelCount >= minLabels && targetLabelCount <= dynamicMaxLabels) {
                     console.log(`优先选择目标间隔: ${targetMaxIntervalMs}ms, 标签数量: ${targetLabelCount}, 时基: ${timeBaseMs}ms`);
                     return targetMaxIntervalMs;
                 }
             }
             
             // 寻找最佳间隔
             for (let interval of candidateIntervals) {
                 if (interval < minIntervalMs) continue;
                 
                 const labelCount = Math.floor(totalDurationMs / interval) + 1;
                 
                 if (labelCount >= minLabels && labelCount <= dynamicMaxLabels) {
                     console.log(`选择时间间隔: ${interval}ms, 标签数量: ${labelCount}, 动态上限: ${dynamicMaxLabels}, 时基: ${timeBaseMs}ms`);
                     return interval;
                 }
             }
             
             // 备选方案：计算一个合理的间隔
             const fallbackInterval = Math.max(minIntervalMs, totalDurationMs / 15);
             console.log(`使用备选时间间隔: ${fallbackInterval.toFixed(2)}ms`);
             return fallbackInterval;
         }
         
         // 全局变量，用于鼠标事件处理
         let enhancedTaskSegments = {};
");

             
             // 跟踪鼠标位置用于智能tooltip定位
             js.AppendLine(@"
         // 智能tooltip定位
         document.addEventListener('mousemove', function(e) {
             document.documentElement.style.setProperty('--mouse-x', e.clientX + 'px');
             document.documentElement.style.setProperty('--mouse-y', e.clientY + 'px');
             
                           // 检测鼠标是否在左边缘或右边缘，动态调整tooltip位置
              const mouseX = e.clientX;
              const windowWidth = window.innerWidth;
              const tooltipWidth = 450; // 估算tooltip宽度，考虑到任务名可能较长
             
             // 移除之前的位置类
             document.querySelectorAll('.stat-label').forEach(label => {
                 label.classList.remove('tooltip-left', 'tooltip-right', 'tooltip-center');
             });
             
             // 根据鼠标位置添加相应的类
             const hoveredElement = e.target.closest('.stat-label');
             if (hoveredElement) {
                 if (mouseX < tooltipWidth / 2) {
                     hoveredElement.classList.add('tooltip-left');
                 } else if (mouseX > windowWidth - tooltipWidth / 2) {
                     hoveredElement.classList.add('tooltip-right');
                 } else {
                     hoveredElement.classList.add('tooltip-center');
                 }
             }
         });

          // 绘制分布图的函数 - 抢占式调度可视化（带层级显示）
                     function drawDistribution() {
             // 根据最大层级动态设置画布高度（任务块高度固定）
             const maxLayer = Math.max(0, ...preemptiveData.filter(p => p.preemptionLevel >= 0).map(p => p.preemptionLevel));
             const requiredLayers = (isFinite(maxLayer) ? maxLayer + 1 : 1);
             const requiredHeight = TOP_MARGIN + BOTTOM_MARGIN + requiredLayers * FIXED_BLOCK_HEIGHT;
             distributionCanvas.height = Math.max(80, requiredHeight);
             const canvasHeight = distributionCanvas.height;
             
             distributionCtx.clearRect(0, 0, canvasWidth, canvasHeight);
             
             // 绘制背景网格 - 精确对齐时间点
             distributionCtx.strokeStyle = '#e0e0e0';
             distributionCtx.lineWidth = 1;
             
             // 使用与时间标签一致的智能间隔
             const interval = calculateTimeInterval(durationMs, timeBaseMs);
             const labelCount = Math.floor(durationMs / interval);
             
             for (let i = 0; i <= labelCount; i++) {
                 const time = interval * i;
                 const x = timeToPixel(time, durationMs, canvasWidth);
                 // 确保最后一条线不会超出画布边界
                 const clippedX = Math.min(x, canvasWidth - 1);
                 distributionCtx.beginPath();
                 distributionCtx.moveTo(clippedX, 0);
                 distributionCtx.lineTo(clippedX, canvasHeight);
                 distributionCtx.stroke();
             }
             
             // 直接使用OffsetValidator提供的抢占层级数据
             const layerData = preemptiveData.map(point => ({
                 timeMs: point.timeMs,
                 executingTask: point.executingTask || '',
                 layer: point.preemptionLevel >= 0 ? point.preemptionLevel : -1
             }));
             
             // 将layerData暴露给全局作用域，供tooltip使用
             window.currentLayerData = layerData;
             
             // 调试输出：显示OffsetValidator提供的数据
             console.log('=== OffsetValidator提供的数据 ===');
             console.log(`抢占式数据点总数: ${preemptiveData.length}`);
             console.log(`任务段数据:`, Object.keys(taskSegments).map(task => `${task}: ${taskSegments[task].length}段`).join(', '));
             
             // 统计层级分布
             const layerStats = {};
             preemptiveData.forEach((point, index) => {
                 if (point.executingTask) {
                     const layer = point.preemptionLevel;
                     if (!layerStats[layer]) layerStats[layer] = 0;
                     layerStats[layer]++;
                     
                     // 显示有层级的数据点
                     if (layer > 0) {
                         console.log(`层级${layer}: 时间=${point.timeMs.toFixed(2)}ms, 任务='${point.executingTask}'`);
                     }
                 }
             });
             
             console.log('层级分布:', Object.keys(layerStats).map(layer => `层级${layer}: ${layerStats[layer]}个点`).join(', '));
             
              // 直接使用TaskSegments中的PreemptionLevel信息
             console.log('=== 处理TaskSegments数据 ===');
             console.log('原始taskSegments数据:', taskSegments);
             
             enhancedTaskSegments = {};
             Object.keys(taskSegments).forEach(taskName => {
                 console.log(`处理任务 ${taskName}: ${taskSegments[taskName].length}个段`);
                 enhancedTaskSegments[taskName] = [];
                 
                 taskSegments[taskName].forEach((segment, segIndex) => {
                     // 检查segment对象的结构
                     console.log(`  原始段${segIndex}:`, segment);
                     
                     // 直接使用段中的层级信息，无需查找
                     const layer = segment.preemptionLevel !== undefined ? segment.preemptionLevel : 0;
                     console.log(`  段${segIndex}: ${segment.startMs.toFixed(2)}-${segment.endMs.toFixed(2)}ms -> 层级${layer} (原始层级: ${segment.preemptionLevel})`);
                     
                     enhancedTaskSegments[taskName].push({
                         startTimeMs: segment.startMs,
                         endTimeMs: segment.endMs,
                         layer: layer,
                         jitterMs: segment.jitterMs || 0
                     });
                 });
             });
             
             console.log('处理后的enhancedTaskSegments:', enhancedTaskSegments);
             
             // 绘制所有任务段
             console.log('=== 绘制任务段信息 ===');
             Object.keys(enhancedTaskSegments).forEach(taskName => {
                 const color = taskColors[taskName] || '#cccccc';
                 const segments = enhancedTaskSegments[taskName];
                 console.log(`绘制任务 ${taskName}: ${segments.length}个段`);
                 
                                 segments.forEach((segment, segIndex) => {
                    const startX = (segment.startTimeMs / durationMs) * canvasWidth;
                    let endX = (segment.endTimeMs / durationMs) * canvasWidth;
                    
                    // 确保任务块有最小显示宽度（3像素，确保极短任务可见）
                    const minWidthPixels = 3;
                    if (endX - startX < minWidthPixels) {
                        endX = startX + minWidthPixels;
                    }
                    
                     const y = canvasHeight - BOTTOM_MARGIN - ((segment.layer + 1) * FIXED_BLOCK_HEIGHT);
                     const height = FIXED_BLOCK_HEIGHT - LAYER_GAP; // 固定高度，留出微小间隙
                    
                    console.log(`  段${segIndex}: ${segment.startTimeMs.toFixed(2)}-${segment.endTimeMs.toFixed(2)}ms, 层级${segment.layer}, 位置(${startX.toFixed(1)}, ${y}, ${(endX-startX).toFixed(1)}, ${height}), 最小宽度已应用`);
                    
                    // 确保不超出画布边界且有有效宽度
                    if (y >= 0 && y + height <= canvasHeight && endX > startX + 0.5) {
                         // 绘制任务段
                         distributionCtx.fillStyle = color;
                         distributionCtx.fillRect(startX, y, Math.max(1, endX - startX), height);
                         
                         // 绘制任务名称（如果段足够宽）
                         if (endX - startX > 20) {
                             distributionCtx.fillStyle = '#ffffff';
                             distributionCtx.font = 'bold 10px Arial';
                             distributionCtx.textAlign = 'center';
                             distributionCtx.shadowColor = 'rgba(0,0,0,0.8)';
                             distributionCtx.shadowBlur = 1;
                             distributionCtx.fillText(
                                 taskName.length > 4 ? taskName.substring(0, 4) : taskName,
                                 (startX + endX) / 2,
                                 y + height / 2 + 3
                             );
                             distributionCtx.shadowBlur = 0; // 重置阴影
                         }
                         
                         // 移除边框绘制
                         
                         console.log(`    ✓ 已绘制 (颜色: ${color}, 层级: ${segment.layer})`);
                     } else {
                         console.log(`    ✗ 跳过绘制 (y=${y}, height=${height}, width=${endX-startX})`);
                     }
                 });
             });
             
         }
         
         // 绘制精确对齐的时间标签函数
         function drawDistributionLabels() {
             const canvasWidth = distributionCanvas.width;
             distributionLabels.innerHTML = '';
             
             // 使用智能时间间隔计算
             const interval = calculateTimeInterval(durationMs, timeBaseMs);
             const labelCount = Math.floor(durationMs / interval);
             
             for (let i = 0; i <= labelCount; i++) {
                 const time = interval * i;
                 const x = timeToPixel(time, durationMs, canvasWidth);
                 
                 const label = document.createElement('div');
                 label.className = 'time-label';
                 
                 // 特殊处理0ms标签位置，避免被左边界遮挡
                 if (i === 0) {
                     label.style.left = Math.max(0, x) + 'px';
                     label.style.transform = 'translateX(0)'; // 取消居中，左对齐
                 } else {
                     label.style.left = x + 'px';
                     label.style.transform = 'translateX(-50%)'; // 居中对齐
                 }
                 
                 // 智能格式化时间标签，根据间隔大小决定精度
                 if (time === 0) {
                     label.textContent = '0ms';
                 } else if (interval < 1) {
                     label.textContent = time.toFixed(1) + 'ms'; // 间隔小于1ms时显示1位小数
                 } else {
                     label.textContent = time.toFixed(0) + 'ms'; // 间隔大于等于1ms时显示整数
                 }
                 
                 distributionLabels.appendChild(label);
             }
         }
         
                                     function drawTaskTimeline() {
             // 使用动态Canvas宽度而不是固定值
               // 根据任务数量动态设置画布高度（任务块高度与分布图一致）
               const leftMargin = 0; // 最小左边距
               const allTasks = Object.keys(taskColors);
               const taskRows = Math.max(1, allTasks.length);
               const requiredTaskCanvasHeight = TOP_MARGIN + BOTTOM_MARGIN + taskRows * FIXED_BLOCK_HEIGHT + (taskRows - 1) * ROW_GAP;
               taskCanvas.height = Math.max(200, requiredTaskCanvasHeight);
               const canvasHeight = taskCanvas.height;
              
              // 计算智能时间间隔，避免重复声明
              const interval = calculateTimeInterval(durationMs, timeBaseMs);
              const labelCount = Math.floor(durationMs / interval);
              
              taskCtx.clearRect(0, 0, canvasWidth, canvasHeight);
              
              // 获取所有任务名称
               const taskHeight = FIXED_BLOCK_HEIGHT - LAYER_GAP; // 与分布图统一的固定任务块高度
              
              // 绘制背景网格 - 使用与时间标签一致的智能间隔
              taskCtx.strokeStyle = '#e0e0e0';
              taskCtx.lineWidth = 1;
              
              for (let i = 0; i <= labelCount; i++) {
                  const time = interval * i;
                  const x = leftMargin + (time / durationMs) * (canvasWidth - leftMargin);
                  // 确保最后一条线不会超出画布边界
                  const clippedX = Math.min(x, canvasWidth - 1);
                  taskCtx.beginPath();
                  taskCtx.moveTo(clippedX, 0);
                  taskCtx.lineTo(clippedX, canvasHeight);
                  taskCtx.stroke();
              }
              
              // 绘制每个任务的时间轴
              allTasks.forEach((taskName, index) => {
                   const y = TOP_MARGIN + index * (FIXED_BLOCK_HEIGHT + ROW_GAP);
                  const color = taskColors[taskName];
                  
                  // 绘制任务执行段 - 使用抢占式调度的实际执行段
                  taskCtx.fillStyle = color;
                  
                  // 从任务段数据获取实际执行时间段
                  const segments = taskSegments[taskName];
                  if (segments) {
                      segments.forEach(segment => {
                          const startX = leftMargin + (segment.startMs / durationMs) * (canvasWidth - leftMargin);
                          let endX = leftMargin + (segment.endMs / durationMs) * (canvasWidth - leftMargin);
                          
                          // 确保任务块有最小显示宽度（3像素，确保极短任务可见）
                          const minWidthPixels = 3;
                          if (endX - startX < minWidthPixels) {
                              endX = startX + minWidthPixels;
                          }
                          
                          if (endX > startX) {
                              // 确保线条至少有3像素宽，便于查看
                              const rectWidth = Math.max(3, endX - startX);
                              taskCtx.fillRect(startX, y + 2, rectWidth, taskHeight - 4);
                          }
                      });
                  }
                  
                  // 绘制分隔线
                  taskCtx.strokeStyle = '#ddd';
                  taskCtx.lineWidth = 1;
                  taskCtx.beginPath();
                  taskCtx.moveTo(leftMargin, y + taskHeight);
                  taskCtx.lineTo(canvasWidth - 1, y + taskHeight); // 确保不超出画布边界
                  taskCtx.stroke();
              });
             
                           // 绘制时间标签
              taskLabels.innerHTML = '';
              
              for (let i = 0; i <= labelCount; i++) {
                  const time = interval * i;
                  const x = leftMargin + (time / durationMs) * (canvasWidth - leftMargin);
                  // 确保时间标签位置不会超出画布边界
                  const clippedX = Math.min(x, canvasWidth - 20); // 预留20px避免标签被裁剪
                  
                  const label = document.createElement('div');
                  label.className = 'time-label';
                  
                  // 特殊处理0ms和最后标签位置
                  if (i === 0) {
                      label.style.left = Math.max(leftMargin, clippedX) + 'px';
                      label.style.transform = 'translateX(0)'; // 取消居中，左对齐
                  } else if (i === labelCount) {
                      label.style.left = clippedX + 'px';
                      label.style.transform = 'translateX(-100%)'; // 右对齐，避免超出边界
                  } else {
                      label.style.left = clippedX + 'px';
                      label.style.transform = 'translateX(-50%)'; // 居中对齐
                  }
                  
                  // 智能格式化时间标签，根据间隔大小决定精度
                  if (time === 0) {
                      label.textContent = '0ms';
                  } else if (interval < 1) {
                      label.textContent = time.toFixed(1) + 'ms'; // 间隔小于1ms时显示1位小数
                  } else {
                      label.textContent = time.toFixed(0) + 'ms'; // 间隔大于等于1ms时显示整数
                  }
                  
                  taskLabels.appendChild(label);
              }
         }
         
         // 鼠标悬停显示详细信息 - 分布图
         distributionCanvas.addEventListener('mousemove', function(e) {
             const rect = distributionCanvas.getBoundingClientRect();
             const x = e.clientX - rect.left;
             const timeMs = pixelToTime(x, durationMs, canvasWidth);
             
             // 基于调整后的任务段数据查找当前时间点的任务
             let currentTaskInfo = null;
             
             // 遍历所有调整后的任务段，找到包含当前时间点的段
             Object.keys(enhancedTaskSegments).forEach(taskName => {
                 const segments = enhancedTaskSegments[taskName];
                 segments.forEach(segment => {
                     if (timeMs >= segment.startTimeMs && timeMs < segment.endTimeMs) {
                         currentTaskInfo = {
                             taskName: taskName,
                             startTimeMs: segment.startTimeMs,
                             endTimeMs: segment.endTimeMs,
                             layer: segment.layer,
                             jitterMs: segment.jitterMs || 0
                         };
                     }
                 });
             });
             
             let tooltipContent = `<strong>时间: ${timeMs.toFixed(2)}ms</strong><br>`;
             
             if (!currentTaskInfo) {
                 tooltipContent += 'CPU状态: 空闲<br>执行任务: 无';
             } else {
                 tooltipContent += `CPU状态: 执行中<br>当前任务: ${currentTaskInfo.taskName}<br>显示层级: ${currentTaskInfo.layer}`;
                 
                 // 显示层级信息
                 if (currentTaskInfo.layer > 0) {
                     tooltipContent += `<br>抢占层级: ${currentTaskInfo.layer} (发生抢占)`;
                 } else if (currentTaskInfo.layer === 0) {
                     tooltipContent += `<br>抢占层级: ${currentTaskInfo.layer} (正常执行)`;
                 }
                 
                 // 显示段的时间信息
                 tooltipContent += `<br>段时间: ${currentTaskInfo.startTimeMs.toFixed(2)}-${currentTaskInfo.endTimeMs.toFixed(2)}ms`;
                 
                 // 查找任务的详细信息
                 const taskData = originalTasks[currentTaskInfo.taskName];
                 if (taskData) {
                     tooltipContent += `<br>任务周期: ${taskData.periodMs}ms<br>任务偏移: ${taskData.offsetMs}ms<br>任务抖动: ${taskData.jitterMs}ms`;
                 }
                 
                 // 显示该段的jitter信息
                 if (currentTaskInfo.jitterMs > 0) {
                     tooltipContent += `<br>段Jitter: ${currentTaskInfo.jitterMs.toFixed(3)}ms`;
                 }
             }
             
             showTooltip(e, tooltipContent);
         });
         
         distributionCanvas.addEventListener('mouseleave', hideTooltip);
         
                   // 鼠标悬停显示详细信息 - 任务图
          taskCanvas.addEventListener('mousemove', function(e) {
              const rect = taskCanvas.getBoundingClientRect();
              const x = e.clientX - rect.left;
              const y = e.clientY - rect.top;
              const leftMargin = 0;
              
              if (x > leftMargin) { // 只在时间轴区域显示tooltip
                  const time = ((x - leftMargin) / (canvasWidth - leftMargin)) * durationMs;
                  
                  // 确定当前鼠标位置对应的任务
                  const allTasks = Object.keys(taskColors);
                  const taskHeight = Math.max(20, (taskCanvas.height / Math.max(allTasks.length, 1)) - 4);
                  const taskIndex = Math.floor(y / (taskHeight + 4));
                  
                  if (taskIndex >= 0 && taskIndex < allTasks.length) {
                      const taskName = allTasks[taskIndex];
                      const taskData = originalTasks[taskName];
                      
                      if (taskData) {
                          const tooltipContent = `<strong>任务: ${taskName}</strong><br>时间: ${time.toFixed(2)}ms<br>周期: ${taskData.periodMs}ms<br>偏移: ${taskData.offsetMs}ms<br>执行时间: ${taskData.executionTimeMs}ms<br>任务抖动: ${taskData.jitterMs}ms`;
                          showTooltip(e, tooltipContent);
                      }
                  }
              }
          });
         
         taskCanvas.addEventListener('mouseleave', hideTooltip);
         
         function showTooltip(e, content) {
             hideTooltip();
             const tooltip = document.createElement('div');
             tooltip.className = 'canvas-tooltip';
             tooltip.innerHTML = content;
             
             // 设置tooltip样式
             tooltip.style.cssText = `
                 position: absolute;
                 background: rgba(0, 0, 0, 0.9);
                 color: white;
                 padding: 12px 16px;
                 border-radius: 8px;
                 font-size: 13px;
                 font-family: 'Microsoft YaHei', Arial, sans-serif;
                 box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                 z-index: 10000;
                 pointer-events: none;
                 white-space: nowrap;
                 line-height: 1.6;
                 max-width: none;
                 min-width: 200px;
                 visibility: hidden;
             `;
             
             // 先添加到页面以获取尺寸
             document.body.appendChild(tooltip);
             
             // 获取页面滚动位置和窗口尺寸
             const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
             const scrollY = window.pageYOffset || document.documentElement.scrollTop;
             const windowWidth = window.innerWidth;
             const windowHeight = window.innerHeight;
             
             // 获取tooltip尺寸
             const tooltipWidthPx = tooltip.offsetWidth;
             const tooltipHeightPx = tooltip.offsetHeight;
             
             // 计算tooltip位置，避免超出窗口边界
             let left = e.clientX + scrollX + 10;
             let top = e.clientY + scrollY - 10;
             
             // 如果右边超出，则显示在鼠标左侧
             if (left + tooltipWidthPx > scrollX + windowWidth) {
                 left = e.clientX + scrollX - tooltipWidthPx - 10;
             }
             
             // 如果上边超出，则显示在鼠标下方
             if (top < scrollY) {
                 top = e.clientY + scrollY + 10;
             }
             
             // 如果下边超出，则显示在鼠标上方
             if (top + tooltipHeightPx > scrollY + windowHeight) {
                 top = e.clientY + scrollY - tooltipHeightPx - 10;
             }
             
             tooltip.style.left = left + 'px';
             tooltip.style.top = top + 'px';
             tooltip.style.visibility = 'visible';
         }
         
         function hideTooltip() {
             const tooltip = document.querySelector('.canvas-tooltip');
             if (tooltip) {
                 tooltip.remove();
             }
         }
         

         
         // 初始化绘制
         drawDistribution();
         drawDistributionLabels();
         drawTaskTimeline();");
             
             return js.ToString();
         }
        
        /// <summary>
        /// 转义JavaScript字符串中的特殊字符
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>转义后的字符串</returns>
        private static string EscapeJavaScriptString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            
            return input
                .Replace("\\", "\\\\")  // 反斜杠
                .Replace("'", "\\'")    // 单引号
                .Replace("\"", "\\\"")  // 双引号
                .Replace("\n", "\\n")   // 换行符
                .Replace("\r", "\\r")   // 回车符
                .Replace("\t", "\\t")   // 制表符
                .Replace("\b", "\\b")   // 退格符
                .Replace("\f", "\\f");  // 换页符
        }
        
        /// <summary>
        /// 获取HTML文件路径
        /// </summary>
        private static string GetHtmlFilePath()
        {
            string exePath = Application.ExecutablePath;
            string exeDirectory = Path.GetDirectoryName(exePath);
            string fileName = $"TaskTimeline_{DateTime.Now:yyyyMMdd_HHmmss}.html";
            return Path.Combine(exeDirectory, fileName);
        }
    }
    
    /// <summary>
    /// 抢占式时间轴数据（扩展OffsetValidator的DetailedTimelineData，添加ValidationResult字段）
    /// </summary>
    public class PreemptiveTimelineData : DetailedTimelineData
    {
        /// <summary>
        /// 验证结果
        /// </summary>
        public ValidationResult ValidationResult { get; set; }
    }
    

    
    /// <summary>
    /// 时间轴统计数据
    /// </summary>
    public class TimelineStatistics
    {
        public int MinTaskCount { get; set; }
        public int MaxTaskCount { get; set; }
        public List<int> MinTimePointsUs { get; set; } = new List<int>();
        public List<int> MaxTimePointsUs { get; set; } = new List<int>();
        public double AverageTaskCount { get; set; }
        public double DurationMs { get; set; }
        public Dictionary<int, int> PreemptionLevelDistribution { get; set; } = new Dictionary<int, int>();
        public Dictionary<string, int> TaskExecutionCounts { get; set; } = new Dictionary<string, int>();
        public double CpuLoadPercentage { get; set; }
        public double PeakLoadPercentage { get; set; }
        public double IdlePercentage { get; set; }
        public ValidationResult ValidationResult { get; set; } = null; // 校验结果，用于显示详细信息
    }
}