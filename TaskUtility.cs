using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace CalculateOffset
{
    /// <summary>
    /// 启发式种子生成的排序策略
    /// </summary>
    public enum HeuristicSortStrategy
    {
        /// <summary>
        /// 默认策略：不可抢占任务优先
        /// </summary>
        NonPreemptiveFirst,
        
        /// <summary>
        /// 备选策略：高优先级任务优先
        /// </summary>
        HighPriorityFirst,
        
        /// <summary>
        /// 第三阶段策略：短周期任务优先
        /// </summary>
        PeriodOptimized,
        
        /// <summary>
        /// 第三阶段策略：长执行时间任务优先
        /// </summary>
        ExecutionTimeOptimized,
        
        /// <summary>
        /// 第三阶段策略：负载均衡导向
        /// </summary>
        LoadBalanced,
        
        /// <summary>
        /// 高级策略：基于OffsetCalculator的优化计算
        /// </summary>
        OffsetCalculatorBased
    }

    /// <summary>
    /// 任务工具类，封装共享的计算逻辑和常量
    /// </summary>
    public static class TaskUtility
    {
        #region 共享常量

        /// <summary>
        /// 内部计算精度因子(无论时基是ms还是us，内部计算精度都是1us)
        /// </summary>
        public const int INTERNAL_PRECISION_FACTOR = 1000; // 1ms = 1000us
        
        /// <summary>
        /// 最小时基值(微秒)，所有时基都必须是此值的整数倍
        /// </summary>
        public const int MIN_TIME_BASE_US = 100; // 最小时基100us
        
        /// <summary>
        /// 最小执行时间(微秒)
        /// </summary>
        public const int MIN_EXECUTION_TIME_US = 10; // 默认最小执行时间为10us
        
        /// <summary>
        /// 最大超周期限制(微秒)，防止内存占用过多
        /// </summary>
        public const int MAX_HYPER_PERIOD_US = 10000000; // 10秒
        
        /// <summary>
        /// 微秒步长，用于时间线生成时的精度控制
        /// </summary>
        public const int US_STEP = 10; // 步长10us

        #endregion

        #region 会话LCM管理

        /// <summary>
        /// 当前会话的LCM值，避免重复计算
        /// </summary>
        private static int _sessionLcm = 0;

        /// <summary>
        /// 设置当前会话的LCM值
        /// </summary>
        /// <param name="tasks">任务列表</param>
        public static void SetSessionLcm(IEnumerable<TaskItem> tasks)
        {
            if (tasks == null)
            {
                _sessionLcm = 0;
                return;
            }

            PerformanceTimer.Start("会话LCM计算(一次性)");
            
            try
            {
                int[] periods = tasks.Where(t => t.PeriodUs.HasValue && t.PeriodUs.Value > 0)
                                     .Select(t => t.PeriodUs ?? 1)
                                     .ToArray();
                
                int lcm = CalculateLcm(periods);
                
                // 限制超周期最大值，防止内存占用过多
                _sessionLcm = Math.Min(lcm, MAX_HYPER_PERIOD_US);
                
                Console.WriteLine($"💾 会话LCM已设置: {_sessionLcm}us ({_sessionLcm/1000.0:F1}ms)");
            }
            finally
            {
                PerformanceTimer.Stop("会话LCM计算(一次性)");
            }
        }

        /// <summary>
        /// 获取当前会话的LCM值
        /// </summary>
        /// <returns>会话LCM值</returns>
        public static int GetSessionLcm()
        {
            return _sessionLcm;
        }

        /// <summary>
        /// 清除会话LCM值
        /// </summary>
        public static void ClearSessionLcm()
        {
            _sessionLcm = 0;
            Console.WriteLine("🗑️ 会话LCM已清除");
        }

        #endregion

        #region OffsetCalculator种子缓存优化

        /// <summary>
        /// OffsetCalculator种子缓存，用于避免重复昂贵计算
        /// </summary>
        private static readonly Dictionary<string, Dictionary<int, int>> _offsetCalculatorSeedCache = 
            new Dictionary<string, Dictionary<int, int>>();

        /// <summary>
        /// 缓存锁，确保线程安全
        /// </summary>
        private static readonly object _cacheLock = new object();

        /// <summary>
        /// 生成缓存键，基于任务特征和参数
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="timeBaseUs">时基</param>
        /// <param name="maxOffsetUs">最大offset限制</param>
        /// <returns>缓存键</returns>
        private static string GenerateOffsetCalculatorCacheKey(List<TaskItem> tasks, int timeBaseUs, int maxOffsetUs)
        {
            // 基于任务特征生成缓存键，确保相同输入产生相同键
            var keyBuilder = new System.Text.StringBuilder();
            keyBuilder.Append($"tb{timeBaseUs}_mo{maxOffsetUs}_tc{tasks.Count}_");
            
            // 添加任务特征哈希（简化版），包含jitter时间
            foreach (var task in tasks.OrderBy(t => t.TaskIndex))
            {
                keyBuilder.Append($"t{task.TaskIndex}p{task.PeriodUs}e{task.ExecutionTimeUs}pr{task.Priority}np{task.NonPreemptive}j{task.JitterTimeUs}_");
            }
            
            return keyBuilder.ToString().GetHashCode().ToString();
        }

        /// <summary>
        /// 清空OffsetCalculator种子缓存（用于测试或内存清理）
        /// </summary>
        public static void ClearOffsetCalculatorSeedCache()
        {
            lock (_cacheLock)
            {
                _offsetCalculatorSeedCache.Clear();
                // OffsetCalculator缓存已清空
            }
        }

        #endregion

        #region 时间转换

        /// <summary>
        /// 将界面输入的毫秒值转换为内部存储的微秒值，向上取整
        /// </summary>
        /// <param name="msValue">毫秒值（支持3位小数精度）</param>
        /// <returns>微秒值（整型）</returns>
        public static int ConvertToMicroseconds(double msValue)
        {
            return (int)Math.Ceiling(msValue * INTERNAL_PRECISION_FACTOR);
        }

        /// <summary>
        /// 将内部存储的微秒值转换为界面显示的毫秒值
        /// </summary>
        /// <param name="usValue">微秒值（整型）</param>
        /// <returns>毫秒值（支持3位小数精度）</returns>
        public static double ConvertToMilliseconds(int usValue)
        {
            return usValue / (double)INTERNAL_PRECISION_FACTOR;
        }



        /// <summary>
        /// 确保时间值是时基的整数倍
        /// </summary>
        /// <param name="timeValue">时间值</param>
        /// <param name="timeBase">时基</param>
        /// <returns>调整后的时间值</returns>
        public static int EnsureTimeBaseMultiple(int timeValue, int timeBase)
        {
            if (timeBase <= 0) return timeValue;
            // 使用整数运算实现四舍五入：(timeValue + timeBase/2) / timeBase * timeBase
            return ((timeValue + timeBase / 2) / timeBase) * timeBase;
        }

        /// <summary>
        /// 获取内部时基微秒值，输入时已确保是MIN_TIME_BASE_US的整数倍
        /// </summary>
        /// <param name="timeBaseUs">时基值（微秒）</param>
        /// <returns>时基值（微秒）</returns>
        public static int GetInternalTimeBaseUs(int timeBaseUs)
        {
            // 确保最小值
            return Math.Max(MIN_TIME_BASE_US, timeBaseUs);
        }

        /// <summary>
        /// 验证时基是否有效（必须是MIN_TIME_BASE_US的整数倍）
        /// </summary>
        /// <param name="timeBaseUs">时基值（微秒）</param>
        /// <returns>是否有效</returns>
        public static bool IsValidTimeBase(int timeBaseUs)
        {
            return timeBaseUs >= MIN_TIME_BASE_US && timeBaseUs % MIN_TIME_BASE_US == 0;
        }

        #endregion

        #region 任务相关计算

        /// <summary>
        /// 按照优先级和周期对任务进行排序
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>排序后的任务列表</returns>
        public static List<TaskItem> SortTasksByPriorityAndPeriod(IEnumerable<TaskItem> tasks)
        {
            return tasks.OrderByDescending(t => t.Priority ?? 0)
                       .ThenBy(t => t.PeriodUs ?? int.MaxValue)
                       .ToList();
        }

        /// <summary>
        /// 获取任务执行时间（微秒）
        /// </summary>
        /// <param name="task">任务</param>
        /// <returns>执行时间（微秒）</returns>
        public static int GetTaskExecutionTimeUs(TaskItem task)
        {
            // 确保最小执行时间(微秒精度)
            return task.ExecutionTimeUs ?? MIN_EXECUTION_TIME_US;
        }

        /// <summary>
        /// 提取有效任务
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <returns>有效任务列表</returns>
        public static List<TaskItem> GetValidTasks(IEnumerable<TaskItem> tasks)
        {
            return tasks.Where(t => t.Enabled && t.PeriodUs.HasValue && t.PeriodUs.Value > 0).ToList();
        }

        /// <summary>
        /// 计算任务超周期（所有任务周期的最小公倍数）
        /// 优先返回会话LCM值，如果未设置则重新计算
        /// </summary>
        /// <param name="tasks">任务列表（可选，如果会话LCM已设置可为null）</param>
        /// <returns>超周期长度（微秒）</returns>
        public static int CalculateHyperPeriod(IEnumerable<TaskItem> tasks = null)
        {
            // 如果会话LCM已设置，直接返回
            if (_sessionLcm > 0)
            {
                return _sessionLcm;
            }

            // 如果没有设置会话LCM且没有提供任务列表，无法计算
            if (tasks == null)
            {
                throw new InvalidOperationException("会话LCM未设置且未提供任务列表，无法计算超周期");
            }

            // 重新计算并设置会话LCM
            SetSessionLcm(tasks);
            return _sessionLcm;
        }

        #endregion

        #region 数学计算

        /// <summary>
        /// 计算两个数的最大公约数
        /// </summary>
        public static int Gcd(int a, int b)
        {
            while (b != 0)
            {
                int temp = b;
                b = a % b;
                a = temp;
            }
            return a;
        }
        
        /// <summary>
        /// 计算两个数的最小公倍数
        /// </summary>
        public static int Lcm(int a, int b)
        {
            return (a / Gcd(a, b)) * b;
        }
        
        /// <summary>
        /// 计算多个数的最小公倍数
        /// </summary>
        public static int CalculateLcm(int[] numbers)
        {
            if (numbers.Length == 0)
                return 1;
            
            int result = numbers[0];
            for (int i = 1; i < numbers.Length; i++)
            {
                result = Lcm(result, numbers[i]);
            }
            
            return result;
        }

        #endregion

        #region 启发式种子生成

        /// <summary>
        /// 生成一个高质量的启发式"种子"解。
        /// 该方法使用贪心策略，按任务的约束强度排序，
        /// 逐个为任务寻找在整个超周期内冲突最小的Offset位置。
        /// </summary>
        /// <param name="tasks">完整的任务列表</param>
        /// <param name="timeBaseUs">时基（微秒）</param>
        /// <param name="maxOffsetUs">最大offset限制（微秒）- 已弃用，现在使用每个任务的MaxOffsetUs</param>
        /// <param name="strategy">启发式排序策略</param>
        /// <returns>一个包含Offset解的字典（TaskIndex -> 微秒值），或在异常时返回null</returns>
        public static Dictionary<int, int> GenerateHeuristicSeed(
            List<TaskItem> tasks, 
            int timeBaseUs, 
            int maxOffsetUs,
            HeuristicSortStrategy strategy = HeuristicSortStrategy.NonPreemptiveFirst)
        {
            try
            {
                var solution = new Dictionary<int, int>();
                if (tasks == null || tasks.Count == 0) return solution;

                // 特殊处理：OffsetCalculator策略
                if (strategy == HeuristicSortStrategy.OffsetCalculatorBased)
                {
                    return GenerateOffsetCalculatorBasedSeed(tasks, timeBaseUs, maxOffsetUs);
                }

                var taskMap = tasks.ToDictionary(t => t.TaskIndex);

                // 1. 拓扑排序：这是处理前置任务的第一步，确保无依赖冲突的放置顺序。
                List<TaskItem> topologicallySortedTasks;
                try
                {
                    topologicallySortedTasks = TaskValidator.TopologicalSort(tasks);
                }
                catch (CircularDependencyException)
                {
                    // 启发式种子生成失败：无法为存在循环依赖的任务生成启发式解
                    return null; // 循环依赖无法生成启发式种子
                }

                // 2. 计算拓扑层级并按层级分组排序
                var taskLevels = CalculateTopologicalLevels(topologicallySortedTasks);
                var sortedTasks = ApplySortingStrategy(taskLevels, strategy);

                // 3. 模拟时间线：计算一个合理的分析窗口（超周期），超周期已经限制最大值，无需再做限制。
                int hyperPeriodUs = CalculateHyperPeriod();
                // 使用数组以获得最佳性能，注意内存占用。
                // 将时间线精度降低到时基级别，以减少内存。
                int timelineSize = hyperPeriodUs / timeBaseUs;

                var timelineLoad = new int[timelineSize];

                // 4. 贪心放置循环 (使用拓扑层级排序后的列表)
                foreach (var task in sortedTasks)
                {
                    int finalOffsetUs;

                    // 不会有无效的任务周期传入，因为已经过滤过

                    // --- A. 处理手动输入的任务 ---
                    if (task.ManualInput && task.OffsetUs.HasValue)
                    {
                        finalOffsetUs = task.OffsetUs.Value;
                    }
                    // --- B. 为自动计算的任务寻找最佳位置 ---
                    else
                    {
                        int executionTimeUs = GetTaskExecutionTimeUs(task);
                        
                        // 计算基于前置任务的最小允许启动时间
                        int minStartTimeUs = CalculateMinStartTimeForHeuristic(task, solution, tasks);
                        
                        int bestOffsetUs = minStartTimeUs; // 从最小启动时间开始
                        int minTotalConflict = int.MaxValue;
                        int periodUs = task.PeriodUs.Value;

                        // 修改搜索范围，使其从最小启动时间开始，在周期范围内搜索，但不超过用户设定的最大offset限制
                        //int searchEndUs = Math.Min(minStartTimeUs + periodUs, maxOffsetUs);
                        for (int currentOffsetUs = minStartTimeUs; currentOffsetUs < minStartTimeUs + periodUs; currentOffsetUs += timeBaseUs)
						{
                            int currentTotalConflict = 0;
                            int instanceCount = hyperPeriodUs / periodUs;
                            if (instanceCount == 0) instanceCount = 1;

                            // 优化：预计算单个实例的冲突
                            int singleInstanceConflict = 0;
                            for (int t = 0; t < executionTimeUs; t += timeBaseUs)
                            {
                                int timePoint = currentOffsetUs + t;
                                int timePointIndex = ((currentOffsetUs + t) % hyperPeriodUs) / timeBaseUs;
                                if(timePointIndex < timelineLoad.Length && timePointIndex >= 0)
                                {
                                    singleInstanceConflict += timelineLoad[timePointIndex];
                                }
                            }

                            // 新增：计算潜在的阻塞成本
                            int blockingCost = 0;
                            if (task.NonPreemptive) 
                            {
                                // 估算如果把task放在这里，它会阻塞哪些更高优先级的任务
                                blockingCost = EstimateBlockingCost(task, currentOffsetUs, tasks, timelineLoad, timeBaseUs, hyperPeriodUs);
                            }

                            // 总冲突 = 重叠冲突 + 阻塞成本
                            currentTotalConflict = (singleInstanceConflict * instanceCount) + blockingCost;

                            if (currentTotalConflict < minTotalConflict)
                            {
                                minTotalConflict = currentTotalConflict;
                                bestOffsetUs = currentOffsetUs;
                            }

                            if (minTotalConflict == 0) break;
                        }
                        finalOffsetUs = (int)bestOffsetUs;
                        
                        // 检查是否超过该任务的最大offset限制
                        int taskMaxOffsetUs = task.MaxOffsetUs;
                        if (finalOffsetUs > taskMaxOffsetUs)
                        {
                            // 任务的启发式offset超出限制，舍弃本次启发式计算
                            return null; // 舍弃整个启发式种子计算
                        }
                    }

                    // 5. 分配Offset并更新时间线
                    solution[task.TaskIndex] = finalOffsetUs;
                    UpdateTimelineLoad(timelineLoad, task, finalOffsetUs, timeBaseUs);
                }

                return solution;
            }
            catch (Exception)
            {
                // 启发式种子生成失败
                return null;
            }
        }

        /// <summary>
        /// 根据指定策略对任务进行排序
        /// </summary>
        /// <param name="taskLevels">任务层级映射</param>
        /// <param name="strategy">排序策略</param>
        /// <returns>排序后的任务列表</returns>
        private static List<TaskItem> ApplySortingStrategy(Dictionary<TaskItem, int> taskLevels, HeuristicSortStrategy strategy)
        {
            var groupedTasks = taskLevels
                .GroupBy(kvp => kvp.Value) // 按拓扑层级分组
                .OrderBy(group => group.Key); // 层级从小到大

            switch (strategy)
            {
                case HeuristicSortStrategy.HighPriorityFirst:
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenByDescending(t => t.Priority ?? 0) // 高优先级优先
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0) // 然后是不可抢占
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue)
                        .ThenByDescending(t => t.ExecutionTimeUs ?? 0))
                    .ToList();

                case HeuristicSortStrategy.PeriodOptimized:
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue) // 短周期最优先
                        .ThenByDescending(t => t.Priority ?? 0) // 然后是高优先级
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0)
                        .ThenByDescending(t => t.ExecutionTimeUs ?? 0))
                    .ToList();

                case HeuristicSortStrategy.ExecutionTimeOptimized:
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenByDescending(t => t.ExecutionTimeUs ?? 0) // 长执行时间最优先
                        .ThenByDescending(t => t.Priority ?? 0) // 然后是高优先级
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0)
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue))
                    .ToList();

                case HeuristicSortStrategy.LoadBalanced:
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenBy(t => CalculateTaskUtilization(t)) // 按利用率升序，先放低利用率任务
                        .ThenByDescending(t => t.Priority ?? 0) // 然后是高优先级
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0)
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue))
                    .ToList();

                case HeuristicSortStrategy.OffsetCalculatorBased:
                    // 这个策略在GenerateHeuristicSeed中已特殊处理，这里不应该执行到
                    // 但为了代码完整性，返回优先级排序
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenByDescending(t => t.Priority ?? 0) // 高优先级优先
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0)
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue))
                    .ToList();

                default: // NonPreemptiveFirst
                    return groupedTasks.SelectMany(group => group.Select(kvp => kvp.Key)
                        .OrderBy(t => t.ManualInput ? 0 : 1) // 手动输入优先
                        .ThenByDescending(t => t.NonPreemptive ? 1 : 0) // 不可抢占优先
                        .ThenByDescending(t => t.Priority ?? 0) // 高优先级优先
                        .ThenBy(t => t.PeriodUs ?? int.MaxValue) // 短周期优先
                        .ThenByDescending(t => t.ExecutionTimeUs ?? 0)) // 长执行时间优先
                    .ToList();
            }
        }

        /// <summary>
        /// 生成基于OffsetCalculator的启发式种子
        /// </summary>
        /// <param name="tasks">任务列表</param>
        /// <param name="timeBaseUs">时基（微秒）</param>
        /// <param name="maxOffsetUs">最大offset限制（微秒）</param>
        /// <returns>offset解决方案字典</returns>
        private static Dictionary<int, int> GenerateOffsetCalculatorBasedSeed(List<TaskItem> tasks, int timeBaseUs, int maxOffsetUs)
        {
            PerformanceTimer.Start("OffsetCalculator种子生成");
            
            try
            {
                // 计算所有任务的最大MaxOffsetUs值，用于OffsetCalculator
                int globalMaxOffsetUs = tasks.Any() ? tasks.Max(t => t.MaxOffsetUs) : maxOffsetUs;
                if (globalMaxOffsetUs <= 0) globalMaxOffsetUs = maxOffsetUs; // 回退到传入的值
                
                // 🎯 保守方案：检查缓存，避免重复昂贵计算
                string cacheKey = GenerateOffsetCalculatorCacheKey(tasks, timeBaseUs, globalMaxOffsetUs);
                
                lock (_cacheLock)
                {
                    if (_offsetCalculatorSeedCache.TryGetValue(cacheKey, out var cachedSolution))
                    {
                        // 缓存命中，复用已计算结果，避免重复昂贵计算
                        return new Dictionary<int, int>(cachedSolution); // 返回副本以避免并发修改
                    }
                }
                
                // 过滤出可优化的任务（非手动输入的任务）
                var optimizableTasks = tasks.Where(t => !t.ManualInput).ToList();
                
                if (optimizableTasks.Count == 0)
                {
                    // 所有任务都已手动设置，返回空解
                    var emptySolution = new Dictionary<int, int>();
                    
                    // 缓存空解结果
                    lock (_cacheLock)
                    {
                        _offsetCalculatorSeedCache[cacheKey] = emptySolution;
                    }
                    
                    return emptySolution;
                }

                // 创建任务副本以避免修改原始任务（TaskIndex会被自动保留）
                var tasksCopy = CloneTasks(tasks);
                
                // 创建OffsetCalculator实例（现在统一使用us）
                var calculator = new OffsetCalculator(timeBaseUs, globalMaxOffsetUs);
                
                // 执行OffsetCalculator快速计算，禁用二分法优化以获得快速初始解
                bool success = calculator.CalculateInitialOffsets(tasksCopy);
                
                if (!success)
                {
                    // OffsetCalculator计算失败
                    return null;
                }
                
                // 提取计算结果，确保不超出每个任务的限制
                var solution = new Dictionary<int, int>();
                var originalTaskMap = tasks.ToDictionary(t => t.TaskIndex, t => t);
                
                foreach (var task in tasksCopy)
                {
                    if (task.OffsetUs.HasValue)
                    {
                        int offsetUs = (int)task.OffsetUs;
                        
                        // 获取该任务的最大offset限制
                        int taskMaxOffsetUs = originalTaskMap.ContainsKey(task.TaskIndex) ? 
                            originalTaskMap[task.TaskIndex].MaxOffsetUs : globalMaxOffsetUs;
                        
                        // 检查是否超出该任务的限制
                        if (offsetUs > taskMaxOffsetUs) // 微秒值直接比较
                        {
                            // 任务的offset超出限制，舍弃本次计算
                            return null;
                        }

                        solution[task.TaskIndex] = offsetUs;
                    }
                }
                
                // 缓存计算结果供后续复用
                lock (_cacheLock)
                {
                    _offsetCalculatorSeedCache[cacheKey] = new Dictionary<int, int>(solution);
                    // 计算结果已缓存，后续调用将直接复用
                }
                
                return solution;
            }
            catch (Exception)
            {
                // OffsetCalculator种子异常
                return null;
            }
            finally
            {
                PerformanceTimer.Stop("OffsetCalculator种子生成");
            }
        }

        /// <summary>
        /// 创建任务列表的深拷贝（公共克隆方法）
        /// </summary>
        /// <param name="originalTasks">原始任务列表</param>
        /// <returns>任务副本列表</returns>
        public static List<TaskItem> CloneTasks(List<TaskItem> originalTasks)
        {
            if (originalTasks == null)
                return null;

            return originalTasks.Select(task => new TaskItem
            {
                TaskIndex = task.TaskIndex,
                TaskName = task.TaskName != null ? string.Copy(task.TaskName) : null,
                PeriodUs = task.PeriodUs,
                ExecutionTimeUs = task.ExecutionTimeUs,
                Priority = task.Priority,
                OffsetUs = task.OffsetUs,
                NonPreemptive = task.NonPreemptive,
                ManualInput = task.ManualInput,
                PrerequisiteTasks = task.PrerequisiteTasks != null ? string.Copy(task.PrerequisiteTasks) : null,
                Enabled = task.Enabled,
                JitterTimeUs = task.JitterTimeUs,
                MaxOffsetUs = task.MaxOffsetUs  // 确保复制新的最大offset限制成员
            }).ToList();
        }

        /// <summary>
        /// 为OffsetCalculator创建任务副本（已弃用，请使用CloneTasks）
        /// </summary>
        /// <param name="originalTasks">原始任务列表</param>
        /// <returns>任务副本</returns>
        [System.Obsolete("请使用 TaskUtility.CloneTasks() 方法")]
        private static List<TaskItem> CloneTasksForOffsetCalculator(List<TaskItem> originalTasks)
        {
            return CloneTasks(originalTasks);
        }

        /// <summary>
        /// 计算任务利用率（执行时间/周期）
        /// </summary>
        /// <param name="task">任务</param>
        /// <returns>利用率</returns>
        private static double CalculateTaskUtilization(TaskItem task)
        {
            if (!task.PeriodUs.HasValue || !task.ExecutionTimeUs.HasValue || task.PeriodUs.Value <= 0)
                return 0.0;
            
            // 直接使用微秒值计算，避免不必要的转换
            return (double)task.ExecutionTimeUs.Value / task.PeriodUs.Value;
        }

        /// <summary>
        /// 计算任务的拓扑层级
        /// </summary>
        /// <param name="topologicallySortedTasks">已拓扑排序的任务列表</param>
        /// <returns>任务到层级的映射</returns>
        private static Dictionary<TaskItem, int> CalculateTopologicalLevels(List<TaskItem> topologicallySortedTasks)
        {
            var levels = new Dictionary<TaskItem, int>();
            var taskMap = topologicallySortedTasks.ToDictionary(t => t.TaskName.Trim(), StringComparer.OrdinalIgnoreCase);

            foreach (var task in topologicallySortedTasks)
            {
                int maxPrereqLevel = -1;

                // 如果有前置任务，找出最大的前置任务层级
                if (!string.IsNullOrWhiteSpace(task.PrerequisiteTasks))
                {
                    var prerequisiteNames = TaskValidator.ParsePrerequisiteTaskNames(task.PrerequisiteTasks);
                    foreach (var prereqName in prerequisiteNames)
                    {
                        if (taskMap.TryGetValue(prereqName, out var prereqTask) && levels.ContainsKey(prereqTask))
                        {
                            maxPrereqLevel = Math.Max(maxPrereqLevel, levels[prereqTask]);
                        }
                    }
                }

                // 当前任务的层级 = 最大前置任务层级 + 1
                levels[task] = maxPrereqLevel + 1;
            }

            return levels;
        }


        /// <summary>
        /// 计算任务基于前置任务约束的最小允许启动时间
        /// </summary>
        /// <param name="task">目标任务</param>
        /// <param name="currentSolution">当前已计算的解</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <returns>最小启动时间（微秒）</returns>
        private static int CalculateMinStartTimeForHeuristic(TaskItem task, Dictionary<int, int> currentSolution, List<TaskItem> allTasks)
        {
            if (string.IsNullOrWhiteSpace(task.PrerequisiteTasks)) return 0;

            int maxPrerequisiteEndTimeUs = 0;
            var prerequisiteNames = TaskValidator.ParsePrerequisiteTaskNames(task.PrerequisiteTasks);
            var taskMap = allTasks.ToDictionary(t => t.TaskName.Trim(), StringComparer.OrdinalIgnoreCase);

            foreach (var name in prerequisiteNames)
            {
                if (taskMap.TryGetValue(name, out var prereqTask))
                {
                    // 检查前置任务是否已经有Offset被计算出来
                    if (currentSolution.TryGetValue(prereqTask.TaskIndex, out int prereqOffsetUs))
                    {
                        int prereqExecUs = GetTaskExecutionTimeUs(prereqTask);
                        maxPrerequisiteEndTimeUs = Math.Max(maxPrerequisiteEndTimeUs, prereqOffsetUs + prereqExecUs);
                    }
                }
            }
            return maxPrerequisiteEndTimeUs;
        }

        /// <summary>
        /// 估算不可抢占任务在指定位置可能造成的阻塞成本（增强版）
        /// </summary>
        /// <param name="task">不可抢占任务</param>
        /// <param name="offsetUs">放置位置（微秒）</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <param name="timelineLoad">当前时间线负载</param>
        /// <param name="timeBaseUs">时基（微秒）</param>
        /// <param name="hyperPeriodUs">超周期（微秒）</param>
        /// <returns>阻塞成本估算值</returns>
        private static int EstimateBlockingCost(TaskItem task, int offsetUs, List<TaskItem> allTasks, 
            int[] timelineLoad, int timeBaseUs, int hyperPeriodUs)
        {
            if (!task.NonPreemptive) return 0;

            int executionTimeUs = GetTaskExecutionTimeUs(task);
            int taskPriority = task.Priority ?? 0;
            int blockingCost = 0;
            int taskPeriodUs = task.PeriodUs.Value;

            // 检查在任务执行期间，有多少更高优先级的任务可能被阻塞
            foreach (var hpTask in allTasks.Where(t => t.TaskIndex != task.TaskIndex && (t.Priority ?? 0) > taskPriority))
            {
                int hpPeriodUs = hpTask.PeriodUs.Value;
                int hpExecutionTimeUs = GetTaskExecutionTimeUs(hpTask);
                int hpDeadlineUs = hpPeriodUs; // 假设截止时间等于周期
                
                // 在一个合理的分析窗口内计算阻塞影响
                int analysisWindowUs = Math.Min(hyperPeriodUs, Math.Max(taskPeriodUs, hpPeriodUs) * 3);
                
                for (int cycle = 0; cycle < analysisWindowUs; cycle += hpPeriodUs)
                {
                    // 计算高优先级任务在这个周期的到达时间
                    int arrivalTime = cycle;
                    
                    // 计算不可抢占任务在这个时间窗口的执行区间
                    int taskCycleStart = (cycle / taskPeriodUs) * taskPeriodUs;
                    int taskInstanceStart = taskCycleStart + offsetUs;
                    int taskInstanceEnd = taskInstanceStart + executionTimeUs;
                    
                    // 检查是否发生阻塞
                    if (arrivalTime >= taskInstanceStart && arrivalTime < taskInstanceEnd)
                    {
                        // 核心改进1：计算实际阻塞时长
                        int actualBlockingDuration = taskInstanceEnd - arrivalTime;
                        
                        // 核心改进2：增强的响应时间估算
                        // 简化WCRT：响应时间 = 阻塞时长 + 自身执行时间 + 高优先级干扰
                        int interferenceTime = CalculateHigherPriorityInterference(hpTask, allTasks, arrivalTime, taskInstanceEnd);
                        int estimatedResponseTime = actualBlockingDuration + hpExecutionTimeUs + interferenceTime;
                        
                        // 核心改进3：截止时间感知的巨大惩罚
                        if (estimatedResponseTime > hpDeadlineUs)
                        {
                            // 这是一个非常糟糕的位置，给予巨大惩罚
                            blockingCost += 100000; // 巨大的常数惩罚，避免这种配置
                        }
                        else
                        {
                            // 如果没有超时，成本与实际阻塞时长和优先级差相关
                            int priorityDifference = (hpTask.Priority ?? 0) - taskPriority;
                            blockingCost += actualBlockingDuration * (priorityDifference + 1);
                        }
                    }
                }
            }

            // 适度归一化，保留关键的惩罚信号
            return Math.Min(blockingCost, executionTimeUs * 50); // 放宽归一化限制，保持惩罚强度
        }

        /// <summary>
        /// 计算高优先级任务间的干扰时间（简化版本）
        /// </summary>
        /// <param name="targetTask">目标高优先级任务</param>
        /// <param name="allTasks">所有任务列表</param>
        /// <param name="arrivalTime">到达时间</param>
        /// <param name="blockingEndTime">阻塞结束时间</param>
        /// <returns>干扰时间（微秒）</returns>
        private static int CalculateHigherPriorityInterference(TaskItem targetTask, List<TaskItem> allTasks, 
            int arrivalTime, int blockingEndTime)
        {
            int interferenceTime = 0;
            int targetPriority = targetTask.Priority ?? 0;
            int interferenceWindow = blockingEndTime - arrivalTime;
            
            // 检查更高优先级的任务在干扰窗口内的影响
            foreach (var higherTask in allTasks.Where(t => 
                t.TaskIndex != targetTask.TaskIndex && 
                (t.Priority ?? 0) > targetPriority))
            {
                int higherPeriodUs = higherTask.PeriodUs.Value;
                int higherExecutionUs = GetTaskExecutionTimeUs(higherTask);
                
                if (higherPeriodUs > 0)
                {
                    // 简化：估算在干扰窗口内可能的抢占次数
                    int possiblePreemptions = Math.Max(0, interferenceWindow / higherPeriodUs + 1);
                    interferenceTime += possiblePreemptions * higherExecutionUs;
                }
            }
            
            return Math.Min(interferenceTime, interferenceWindow); // 限制在合理范围内
        }

        /// <summary>
        /// 辅助方法：更新时间线上的负载计数。
        /// </summary>
        private static void UpdateTimelineLoad(int[] timelineLoad, TaskItem task, int offsetUs, int timeBaseUs)
        {
            int hyperPeriodUs = timelineLoad.Length * timeBaseUs;
            if (hyperPeriodUs == 0) return;

            int periodUs = task.PeriodUs ?? 1000; // 默认1ms=1000us
            if (periodUs <= 0) return;

            int executionTimeUs = GetTaskExecutionTimeUs(task);
            int startOffsetUs = offsetUs;

            for (int startTime = startOffsetUs; startTime < hyperPeriodUs; startTime += periodUs)
            {
                for (int t = 0; t < executionTimeUs; t += timeBaseUs)
                {
                    int timePoint = startTime + t;
                    // 使用安全的取模方法，避免负数取模问题
                    int timePointIndex = (timePoint % hyperPeriodUs) / timeBaseUs;
                    if(timePointIndex < timelineLoad.Length && timePointIndex >= 0)
                    {
                        timelineLoad[timePointIndex]++;
                    }
                }
            }
        }

        /// <summary>
        /// 计算时间线负载方差（轻量级代理指标）。
        /// 在超周期窗口内，以给定时基构建一维时间线，将所有任务实例的执行覆盖叠加后计算方差。
        /// 方差越大，负载越不均匀，潜在拥挤/冲突越严重。
        /// </summary>
        /// <param name="tasks">任务列表（需包含有效的 Offset/Period/ExecutionTime）</param>
        /// <param name="timeBaseUs">时基(微秒)。建议使用内部时基或最小时基</param>
        /// <param name="maxAnalysisWindowUs">分析窗口上限(微秒)，默认2000ms</param>
        /// <returns>时间线负载的方差（非归一化）</returns>
        public static double ComputeTimelineLoadVariance(List<TaskItem> tasks, int timeBaseUs, int maxAnalysisWindowUs = 2000000)
        {
            if (tasks == null || tasks.Count == 0)
            {
                return 0.0;
            }

            var valid = tasks.Where(t => t != null && t.Enabled &&
                                         t.OffsetUs.HasValue && t.PeriodUs.HasValue && t.ExecutionTimeUs.HasValue &&
                                         t.PeriodUs.Value > 0 && t.ExecutionTimeUs.Value > 0)
                              .ToList();

            if (valid.Count == 0)
            {
                return 0.0;
            }

            int hyperUs = CalculateHyperPeriod();
            if (hyperUs <= 0)
            {
                return 0.0;
            }
            if (hyperUs > maxAnalysisWindowUs)
            {
                hyperUs = maxAnalysisWindowUs;
            }

            int step = Math.Max(MIN_TIME_BASE_US, timeBaseUs);
            if (step <= 0)
            {
                step = MIN_TIME_BASE_US;
            }

            int timelineSize = hyperUs / step;
            if (timelineSize <= 0)
            {
                return 0.0;
            }

            var timelineLoad = new int[timelineSize];

            foreach (var task in valid)
            {
                int offsetUs = task.OffsetUs ?? 0;
                // 对齐到时基，保证索引一致
                offsetUs = EnsureTimeBaseMultiple(offsetUs, step);
                UpdateTimelineLoad(timelineLoad, task, offsetUs, step);
            }

            // 计算方差（以double进行）
            double mean = 0.0;
            for (int i = 0; i < timelineLoad.Length; i++)
            {
                mean += timelineLoad[i];
            }
            mean /= timelineLoad.Length;

            double variance = 0.0;
            for (int i = 0; i < timelineLoad.Length; i++)
            {
                double diff = timelineLoad[i] - mean;
                variance += diff * diff;
            }
            variance /= timelineLoad.Length;

            return variance;
        }

        #endregion
    }
    
    /// <summary>
    /// 三级挑选缓存管理器 - 实现三级挑选机制的一级挑选（公共版本）
    /// </summary>
    public class ThreeTierSelectionCache
    {
        private double _bestCost = double.MaxValue;
        private readonly List<Dictionary<int, int>> _cachedSolutions = new List<Dictionary<int, int>>();
        private readonly List<TaskItem> _taskTemplate;
        private readonly string _algorithmName;
        
        public ThreeTierSelectionCache(List<TaskItem> taskTemplate, string algorithmName)
        {
            _taskTemplate = taskTemplate;
            _algorithmName = algorithmName;
        }
        
        /// <summary>
        /// 尝试添加新解到缓存
        /// </summary>
        /// <param name="solution">候选解</param>
        /// <param name="cost">解的成本</param>
        /// <returns>是否接受了该解</returns>
        public bool TryAddSolution(Dictionary<int, int> solution, double cost)
        {
            const double COST_TOLERANCE = 1e-6; // 适度容差减少伪不同解，与统一选择保持一致
            
            if (cost < _bestCost - COST_TOLERANCE)
            {
                // 发现更优成本，清空缓存并添加新解
                _bestCost = cost;
                _cachedSolutions.Clear();
                _cachedSolutions.Add(new Dictionary<int, int>(solution));
                return true;
            }
            else if (Math.Abs(cost - _bestCost) <= COST_TOLERANCE)
            {
                // 成本相同，检查是否重复
                if (!IsSolutionDuplicate(solution))
                {
                    _cachedSolutions.Add(new Dictionary<int, int>(solution));
                    return true;
                }
                return false; // 重复解，不接受
            }
            
            return false; // 成本更差，不接受
        }
        
        /// <summary>
        /// 获取最佳成本
        /// </summary>
        public double GetBestCost()
        {
            return _bestCost;
        }
        
        /// <summary>
        /// 获取缓存的解数量
        /// </summary>
        public int GetCachedCount()
        {
            return _cachedSolutions.Count;
        }
        
        /// <summary>
        /// 执行一级挑选：从缓存的相同成本解中选出最优解（多线程优化版本）
        /// </summary>
        /// <param name="progressCallback">进度回调函数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>最优解</returns>
        public Dictionary<int, int> SelectBestFromCache(Action<int, string, string> progressCallback = null, 
            System.Threading.CancellationToken cancellationToken = default)
        {
            if (_cachedSolutions.Count == 0)
                return null;
                
            if (_cachedSolutions.Count == 1)
                return new Dictionary<int, int>(_cachedSolutions[0]);

            // 检测CPU核心数
            int coreCount = Environment.ProcessorCount;
            int optimalThreads = Math.Max(1, Math.Min(coreCount, _cachedSolutions.Count / 10)); // 每线程至少处理10个候选解
            
            //Console.WriteLine($"🔍 [{_algorithmName}] 缓存候选解数量: {_cachedSolutions.Count}");
            //Console.WriteLine($"💻 CPU核心数: {coreCount}, 使用线程数: {optimalThreads}");
            
            progressCallback?.Invoke(5, $"正在构建 {_cachedSolutions.Count} 个候选解", $"使用 {optimalThreads} 个线程并行处理");

            // 从多个成本相同的解中选择最优解
            PerformanceTimer.Start($"{_algorithmName}候选解构建");
            
            // 多线程候选解构建
            var candidates = new StrategyCandidateResult[_cachedSolutions.Count];
            var processedCount = 0;
            var lockObj = new object();
            
            var parallelOptions = new System.Threading.Tasks.ParallelOptions
            {
                MaxDegreeOfParallelism = optimalThreads,
                CancellationToken = cancellationToken
            };

            try
            {
                System.Threading.Tasks.Parallel.ForEach(_cachedSolutions.Select((solution, index) => new { solution, index }), 
                    parallelOptions, item =>
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        
                        // 为挑选函数创建临时OptimizationResultSummary
                        var tempTasks = CloneTaskTemplate(_taskTemplate);
                        ApplySolutionToTasks(item.solution, tempTasks);
                        
                        var tempResult = new OptimizationResultSummary
                        {
                            Algorithm = _algorithmName,
                            Success = true,
                            BestCost = _bestCost,
                            OptimizedTasks = tempTasks
                        };
                        
                        candidates[item.index] = new StrategyCandidateResult
                        {
                            Algorithm = _algorithmName,
                            Strategy = "CachedSolution",
                            Cost = _bestCost,
                            Solution = item.solution,
                            OriginalResult = tempResult
                        };
                        
                        // 线程安全的进度更新
                        lock (lockObj)
                        {
                            processedCount++;
                            if (processedCount % 20 == 0 || processedCount == _cachedSolutions.Count)
                            {
                                int percentage = Math.Min(50, 5 + (processedCount * 45 / _cachedSolutions.Count));
                                progressCallback?.Invoke(percentage, 
                                    $"已处理 {processedCount}/{_cachedSolutions.Count} 个候选解", 
                                    $"多线程并行处理中...");
                            }
                        }
                    });
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine($"🚫 [{_algorithmName}] 候选解构建被用户取消，立即停止");
                PerformanceTimer.Stop($"{_algorithmName}候选解构建");
                throw; // 立即抛出异常，不执行后续代码
            }
            
            // 检查是否在循环后被取消
            cancellationToken.ThrowIfCancellationRequested();
            
            var candidatesList = candidates.Where(c => c != null).ToList();
            
            PerformanceTimer.Stop($"{_algorithmName}候选解构建");
            Console.WriteLine($"✅ [{_algorithmName}] 多线程候选解构建完成，共 {candidatesList.Count} 个");
            
            progressCallback?.Invoke(55, "候选解构建完成", "开始统一挑选最优解...");
            
            // 使用统一挑选函数选择最优解
            PerformanceTimer.Start($"{_algorithmName}统一挑选");
            
            try
            {
                var bestCandidate = IntelligentSchedulingEngine.SelectBestByNewCriteria(candidatesList, progressCallback, cancellationToken);
                PerformanceTimer.Stop($"{_algorithmName}统一挑选");
                
                Console.WriteLine($"🎯 [{_algorithmName}] 最优解选择完成");
                progressCallback?.Invoke(100, "最优解选择完成", "");
                
                return new Dictionary<int, int>(bestCandidate.Solution);
            }
            catch (OperationCanceledException)
            {
                Console.WriteLine($"🚫 [{_algorithmName}] 统一挑选被用户取消");
                PerformanceTimer.Stop($"{_algorithmName}统一挑选");
                throw; // 立即抛出异常
            }
        }
        
        /// <summary>
        /// 检查解是否重复
        /// </summary>
        private bool IsSolutionDuplicate(Dictionary<int, int> newSolution)
        {
            return _cachedSolutions.Any(cached => AreSolutionsEqual(cached, newSolution));
        }
        
        /// <summary>
        /// 比较两个解是否相等
        /// </summary>
        private static bool AreSolutionsEqual(Dictionary<int, int> solution1, Dictionary<int, int> solution2)
        {
            if (solution1.Count != solution2.Count)
                return false;
                
            return solution1.All(kvp => solution2.ContainsKey(kvp.Key) && solution2[kvp.Key] == kvp.Value);
        }
        
        /// <summary>
        /// 克隆任务模板
        /// </summary>
        private List<TaskItem> CloneTaskTemplate(List<TaskItem> template)
        {
            return template.Select(task => new TaskItem
            {
                TaskIndex = task.TaskIndex,
                TaskName = task.TaskName,
                PeriodUs = task.PeriodUs,
                ExecutionTimeUs = task.ExecutionTimeUs,
                Priority = task.Priority,
                OffsetUs = task.OffsetUs,
                NonPreemptive = task.NonPreemptive,
                ManualInput = task.ManualInput,
                PrerequisiteTasks = task.PrerequisiteTasks,
                MaxOffsetUs = task.MaxOffsetUs  // 确保复制新的最大offset限制成员
            }).ToList();
        }
        
        /// <summary>
        /// 应用解到任务列表
        /// </summary>
        private void ApplySolutionToTasks(Dictionary<int, int> solution, List<TaskItem> tasks)
        {
            foreach (var task in tasks)
            {
                if (solution.ContainsKey(task.TaskIndex))
                {
                    task.OffsetUs = solution[task.TaskIndex];
                }
            }
        }
    }
} 
